# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'action_form.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON>rush, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QPushButton, QSizePolicy,
    QWidget)

class Ui_ActionForm(object):
    def setupUi(self, ActionForm):
        if not ActionForm.objectName():
            ActionForm.setObjectName(u"ActionForm")
        ActionForm.resize(255, 92)
        ActionForm.setStyleSheet(u"*{\n"
"	font: 11pt;\n"
"}")
        self.gridLayout_2 = QGridLayout(ActionForm)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.groupBox = QGroupBox(ActionForm)
        self.groupBox.setObjectName(u"groupBox")
        self.gridLayout = QGridLayout(self.groupBox)
        self.gridLayout.setObjectName(u"gridLayout")
        self.title_label = QLabel(self.groupBox)
        self.title_label.setObjectName(u"title_label")

        self.gridLayout.addWidget(self.title_label, 0, 0, 1, 1)

        self.gesture_combobox = QComboBox(self.groupBox)
        self.gesture_combobox.setObjectName(u"gesture_combobox")

        self.gridLayout.addWidget(self.gesture_combobox, 0, 1, 1, 1)

        self.delete_pushbutton = QPushButton(self.groupBox)
        self.delete_pushbutton.setObjectName(u"delete_pushbutton")
        sizePolicy = QSizePolicy(QSizePolicy.Minimum, QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.delete_pushbutton.sizePolicy().hasHeightForWidth())
        self.delete_pushbutton.setSizePolicy(sizePolicy)
        self.delete_pushbutton.setMaximumSize(QSize(30, 30))

        self.gridLayout.addWidget(self.delete_pushbutton, 0, 2, 1, 1)

        self.start_pushbutton = QPushButton(self.groupBox)
        self.start_pushbutton.setObjectName(u"start_pushbutton")

        self.gridLayout.addWidget(self.start_pushbutton, 1, 0, 1, 1)

        self.progressBar = QProgressBar(self.groupBox)
        self.progressBar.setObjectName(u"progressBar")
        self.progressBar.setValue(24)
        self.progressBar.setTextVisible(False)

        self.gridLayout.addWidget(self.progressBar, 1, 1, 1, 2)

        self.gridLayout.setColumnStretch(1, 1)

        self.gridLayout_2.addWidget(self.groupBox, 0, 0, 1, 1)


        self.retranslateUi(ActionForm)

        QMetaObject.connectSlotsByName(ActionForm)
    # setupUi

    def retranslateUi(self, ActionForm):
        ActionForm.setWindowTitle(QCoreApplication.translate("ActionForm", u"Form", None))
        self.groupBox.setTitle("")
        self.title_label.setText("")
        self.delete_pushbutton.setText(QCoreApplication.translate("ActionForm", u"\u2716", None))
        self.start_pushbutton.setText(QCoreApplication.translate("ActionForm", u"start", None))
    # retranslateUi

