import os
import pickle
import pprint

from ruamel.yaml import <PERSON><PERSON><PERSON>
from typing import List, Any, Optional

from src.config.grips import DEFAULT_GRIPS_CONFIG
from src.path import APP_ROOT_PATH

yaml = YAML()

# 全部配置容器
_config = {
    "ALL": None,
    "BASIC": None,
    "REM_ROBOT": None,
    "PROTO_CONFIG": None,
    "ARMBAND_SERIAL_CONFIG": None,
    "HAND_SERIAL_CONFIG": None,
    "ACTION_PARAMS_CONFIG": None,
    "MODEL_CONFIG": None,
    "FILTER": None,
    "SETTINGS_FILE_PATH": "",
}


GRIPS_CONFIG_PATH = os.path.join(APP_ROOT_PATH, ".BCDescription.bci")


def save_grips_config(config):
    with open(GRIPS_CONFIG_PATH, "wb") as file:
        pickle.dump(config, file)


if not os.path.exists(GRIPS_CONFIG_PATH):
    save_grips_config(DEFAULT_GRIPS_CONFIG)


with open(GRIPS_CONFIG_PATH, 'rb') as f:
    content = pickle.load(f)
    GRIPS_CONFIG = content['grips']
    SINGLE_FINGER_CONFIG = content["single_finger"]
    pprint.pprint(content)

    index = {}
    for item in GRIPS_CONFIG + SINGLE_FINGER_CONFIG:
        key = int(item["id"])
        index[key] = item
    GRIPS_ID_MAP = index


def _load_all_sections(config_dict):
    _config["ALL"] = config_dict
    _config["BASIC"] = config_dict.get("BASIC")
    _config["REM_ROBOT"] = config_dict.get("REM_ROBOT")
    _config["PROTO_CONFIG"] = config_dict.get("PROTO_CONFIG")
    _config["ARMBAND_SERIAL_CONFIG"] = config_dict.get("ARMBAND_SERIAL_CONFIG")
    _config["HAND_SERIAL_CONFIG"] = config_dict.get("HAND_SERIAL_CONFIG")
    _config["ACTION_PARAMS_CONFIG"] = config_dict.get("ACTION_PARAMS_CONFIG")
    _config["MODEL_CONFIG"] = config_dict.get("MODEL_CONFIG")
    _config["FILTER"] = config_dict.get("FILTER")


def register_parameters_to_global(settings_file):
    with open(settings_file, 'r', encoding='utf-8') as f:
        config = yaml.load(f)

    if not isinstance(config, dict):
        raise TypeError(f"Invalid YAML structure in file: {settings_file}")

    _config["SETTINGS_FILE_PATH"] = settings_file
    _load_all_sections(config)


def reload_config(new_config):
    if not isinstance(new_config, dict):
        raise TypeError("Expected a dict for config")
    _load_all_sections(new_config)
    print("[INFO] TEST_CONFIG Reloaded:", _config["TEST_CONFIG"])


def get_config(base: str, sub_keys: Optional[List[str]] = None) -> Any:
    """
    获取配置项，支持父路径为点分隔字符串，子路径为列表形式：

    示例：
    - get_config("BASIC") → 返回整个 BASIC
    - get_config("BASIC.model_config", ["version"])
    - get_config("TEST_CONFIG.nested", ["subkey1", "subkey2"])

    :param base: 可以是 'BASIC' 或 'BASIC.subkey1.subkey2' 形式的路径
    :param sub_keys: 子路径，列表形式。默认 None 表示只访问 base
    :param default: 找不到时返回的默认值
    """
    current = _config

    base_keys = base.split(".")
    if len(base_keys) > 1:
        # 处理 base 路径
        for key in base_keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                raise KeyError(f"Key path not found: {' -> '.join(base_keys)}")
    elif len(base_keys) == 1:
        current = current[base_keys[0]]
    else:
        raise KeyError("Invalid base path")

    # 如果没有子路径，直接返回 base 节点结果
    if not sub_keys:
        return current

    # 继续处理子路径
    for key in sub_keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            raise KeyError(f"Key path not found: {base} -> {' -> '.join(sub_keys)}")

    return current


def save_config(config):
    with open(_config["SETTINGS_FILE_PATH"], 'w', encoding='utf-8') as f:
        yaml.dump(config, f)




