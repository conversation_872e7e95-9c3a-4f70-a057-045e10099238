BASIC:
  window_in_seconds: 3

TOOL_CONFIG:
  device_type: SERIAL  # BLE, SERIAL

REM_ROBOT:
  hand_side_type: Left # Left or Right
  ip: ************
  port: 1
  baudrate: 115200
  timeout: 5


PROTO_CONFIG:
  proto_path_structure:
  - [.]
  - [edu-dongle-sensor-protobuf, generated, python]
  header_version: 2
  header:
    magic_numbers:
    - !!binary |
      Qg==
    - !!binary |
      Ug==
    - !!binary |
      Tg==
    - !!binary |
      Qw==
    payload_version: 12
    payload_length: 2
    flag: 0
    packet_crc16: 2
  proto_class:
    1,3:
    - AppSensor
    - SensorApp


ARMBAND_SERIAL_CONFIG:
  port: COM14
  baudrate: 115200


HAND_SERIAL_CONFIG:
  port: COM8
  baudrate: 115200
  device_id: 2


ACTION_PARAMS_CONFIG:
  duration_s: 5
  minimum_action: 3
  minimum_file_kb: 50


MODEL_CONFIG:
  name: newnew.h5


FILTER:
# hp 高通  bs: 带阻   lp: 低通  bp: 带通。  下面结构的顺序代表滤波器的应用顺序
- bs: [4, 49, 51]
- hp: [4, 10]
