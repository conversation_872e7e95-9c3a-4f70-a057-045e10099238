import os.path
import sys
import serial
import serial.tools.list_ports
import socket
import time

from PySide6.QtGui import QIcon
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QRadioButton, QGroupBox, QLabel,
                               QLineEdit, QComboBox, QPushButton, QMessageBox,
                               QFrame, QSpacerItem, QSizePolicy, QGridLayout,
                               QButtonGroup, QDialog, QProgressDialog, QSpinBox, QAbstractSpinBox)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QSize, QThread

from src.config.config import ControlMode
from src.config.device_config import is_armband_device, format_port_info
from robotic_arm_package.robotic_arm import Arm, RM65
from src.path import APP_ROOT_PATH
from src.utils.config_loader import get_config
from src.utils.serial_helper import serial_ports


class ConnectionTestThread(QThread):
    test_completed = Signal(bool, str)  # 测试完成信号：(成功, 错误信息)
    progress_updated = Signal(str)  # 进度更新信号

    def __init__(self, mode, params):
        super().__init__()
        self.mode = mode
        self.params = params

    def run(self):
        try:
            # 测试臂环连接
            self.progress_updated.emit("正在测试臂环连接...")
            if not self.test_armband_connection():
                self.test_completed.emit(False, "臂环连接失败：无法连接到指定的COM口")
                return

            # 测试灵巧手连接
            if self.mode in [ControlMode.HAND]:
                self.progress_updated.emit("正在测试灵巧手连接...")
                if not self.test_hand_connection():
                    self.test_completed.emit(False, "灵巧手连接失败：无法连接到指定的COM口")
                    return

            self.progress_updated.emit("所有连接测试完成")
            self.test_completed.emit(True, "")

        except Exception as e:
            self.test_completed.emit(False, f"连接测试过程中发生错误：{str(e)}")

    def test_armband_connection(self):
        try:
            port = self.params.get('armband_com_port')
            if not port:
                return False

            ser = serial.Serial(
                port=port,
                baudrate=9600,
                timeout=2.0
            )

            if ser.is_open:
                ser.close()
                return True
            else:
                return False

        except Exception as e:
            print(f"臂环连接测试失败: {e}")
            return False

    def test_hand_connection(self):
        try:
            port = self.params.get('hand_com_port')
            if not port:
                return False

            # 尝试打开串口
            ser = serial.Serial(
                port=port,
                baudrate=9600,
                timeout=2.0
            )

            if ser.is_open:
                ser.close()
                return True
            else:
                return False

        except Exception as e:
            print(f"灵巧手连接测试失败: {e}")
            return False


class ConnectionInterface(QDialog):
    # 连接信号
    connection_established = Signal(ControlMode, dict)  # 连接模式, 连接参数

    def __init__(self):
        super().__init__()

        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f2f5, stop:1 #e8eaf0);
            }
        """)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)

        # 标题区域
        self.create_title_section(main_layout)

        # 主要内容区域（左右布局）
        self.create_main_content_section(main_layout)

        # 底部按钮区域
        self.create_button_section(main_layout)

        # 设置窗口属性
        self.setWindowTitle("设备连接配置")
        self.setModal(True)  # 设置为模态对话框
        self.resize(900, 700)

        # 初始化界面状态
        self.current_mode = ControlMode.HAND  # 默认选择手控模式
        self.update_connection_params()

    def create_title_section(self, parent_layout):
        title_widget = QWidget()
        title_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 10px;
            }
        """)

        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(2, 2, 2, 2)
        title_layout.setSpacing(2)

        # 主标题
        main_title = QLabel("臂环灵巧手遥操作控制系统")
        main_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        main_title.setAlignment(Qt.AlignCenter)

        # 副标题
        sub_title = QLabel("请选择控制模式并配置连接参数")
        sub_title.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 16px;
                background: transparent;
            }
        """)
        sub_title.setAlignment(Qt.AlignCenter)

        title_layout.addWidget(main_title)
        title_layout.addWidget(sub_title)

        parent_layout.addWidget(title_widget)

    def create_main_content_section(self, parent_layout):
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(10)
        content_layout.setContentsMargins(0, 0, 0, 0)

        # 左侧：模式选择区域
        self.create_mode_selection_section(content_layout)

        # 右侧：连接参数配置区域
        self.create_connection_params_section(content_layout)

        parent_layout.addWidget(content_widget)

    def create_mode_selection_section(self, parent_layout):
        mode_widget = QWidget()
        mode_widget.setFixedWidth(280)
        mode_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
            }
        """)

        mode_layout = QVBoxLayout(mode_widget)
        mode_layout.setContentsMargins(20, 25, 20, 25)
        mode_layout.setSpacing(10)

        # 区域标题
        section_title = QLabel("控制模式")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                border: none;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                padding-bottom: 10px;
            }
        """)
        mode_layout.addWidget(section_title)

        # 模式数据
        modes_data = [
            (ControlMode.HAND, "手控模式", "直接操作灵巧手", "#FF6B6B", "🖐️"),
            (ControlMode.ARM, "臂控模式", "直接操作机械臂", "#4ECDC4", "🦾"),
            (ControlMode.ARM_HAND, "臂带手模式", "通过机械臂引导灵巧手", "#45B7D1", "🤖"),
            (ControlMode.COOPERATIVE, "协同控制模式", "同步操作臂与手", "#96CEB4", "⚡")
        ]

        self.mode_buttons = QButtonGroup()
        self.mode_cards = {}

        # 创建列表形式的模式选择
        for i, (mode_id, title, desc, color, icon) in enumerate(modes_data):
            card = self.create_mode_list_item(mode_id, title, desc, color, icon)
            self.mode_cards[mode_id] = card
            mode_layout.addWidget(card)

        # 添加弹性空间
        mode_layout.addStretch()

        parent_layout.addWidget(mode_widget)

    def create_mode_list_item(self, mode_id, title, description, color, icon):
        card = QPushButton()
        card.setCheckable(True)
        card.setFixedHeight(80)

        # 默认选择第一个模式
        if mode_id == ControlMode.HAND:
            card.setChecked(True)

        card.setStyleSheet(f"""
            QPushButton {{
                background: white;
                border: 2px solid #f0f0f0;
                border-radius: 10px;
                text-align: left;
                padding: 15px;
                font-size: 14px;
                margin: 2px;
            }}
            QPushButton:hover {{
                border-color: {color};
                background: #f8f9fa;
                transform: translateY(-1px);
            }}
            QPushButton:checked {{
                border-color: {color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}15, stop:1 {color}05);
                font-weight: bold;
            }}
        """)

        # 创建自定义布局 - 直接在按钮上创建布局
        layout = QHBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)

        # 图标标签
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {color};
                background: transparent;
                border: none;
                min-width: 40px;
                max-width: 40px;
            }}
        """)
        icon_label.setAlignment(Qt.AlignCenter)

        # 文本区域
        text_widget = QWidget()
        text_widget.setStyleSheet("QWidget { background: transparent; border: none;}")
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(10, 5, 5, 5)
        text_layout.setSpacing(2)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: none;
                background: transparent;
            }
        """)

        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666;
                border: none;
                background: transparent;
            }
        """)

        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)

        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        layout.addStretch()

        # 连接信号
        card.clicked.connect(lambda : self.on_mode_selected(mode_id))

        # 添加到按钮组
        self.mode_buttons.addButton(card)

        return card

    def on_mode_selected(self, mode_id):
        self.current_mode = mode_id
        self.update_connection_params()

    def create_connection_params_section(self, parent_layout):
        self.params_widget = QWidget()
        self.params_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
            }
        """)

        self.params_layout = QVBoxLayout(self.params_widget)
        self.params_layout.setContentsMargins(10, 10, 30, 25)
        self.params_layout.setSpacing(10)

        # 区域标题
        self.params_title = QLabel("连接参数配置")
        self.params_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 2px solid #f0f0f0;
            }
        """)
        self.params_layout.addWidget(self.params_title)

        # 臂环参数区域（始终显示）
        self.armband_params_widget = self.create_armband_params_widget()
        self.params_layout.addWidget(self.armband_params_widget)

        # 灵巧手参数区域
        self.hand_params_widget = self.create_hand_params_widget()
        self.params_layout.addWidget(self.hand_params_widget)

        # 机械臂参数区域
        self.arm_params_widget = self.create_arm_params_widget()
        self.params_layout.addWidget(self.arm_params_widget)

        # 添加弹性空间
        self.params_layout.addStretch()

        parent_layout.addWidget(self.params_widget)

    def create_armband_params_widget(self):
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: #fff8e1;
                border: 1px solid #ffcc02;
                border-radius: 12px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)

        # 臂环标题
        armband_title = QLabel("📡 臂环连接参数")
        armband_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #FF8F00;
                border: none;
                background: transparent;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(armband_title)

        # COM口选择 - 横向布局
        com_container = QWidget()
        com_container.setStyleSheet("QWidget { background: transparent; }")
        com_layout = QHBoxLayout(com_container)
        com_layout.setContentsMargins(0, 0, 0, 0)
        com_layout.setSpacing(15)

        com_label = QLabel("端口号:")
        com_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                background: transparent;
                min-width: 80px;
            }
        """)

        com_input_layout = QHBoxLayout()
        com_input_layout.setSpacing(5)

        self.armband_com_combo = QComboBox()
        self.armband_com_combo.setStyleSheet("""
            QComboBox {
                padding: 12px 16px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                background: white;
                font-size: 14px;
                min-height: 20px;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #FF8F00;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::item:selected {
                color: black;
                background-color: #FF6B6B;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6b7280;
                margin-right: 10px;
            }
        """)
        self.refresh_armband_com_ports()

        armband_refresh_button = QPushButton()
        armband_refresh_button.setFixedSize(44, 44)
        armband_refresh_button.setIcon(QIcon(os.path.join(APP_ROOT_PATH, "material", "refresh.png")))
        armband_refresh_button.setIconSize(QSize(32, 32))
        armband_refresh_button.setStyleSheet("""
            QPushButton {
                background: #FF8F00;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 28px;
            }
            QPushButton:hover {
                background: #F57C00;
            }
            QPushButton:pressed {
                background: #E65100;
            }
        """)
        armband_refresh_button.clicked.connect(self.refresh_armband_com_ports)

        com_input_layout.addWidget(self.armband_com_combo)
        com_input_layout.addWidget(armband_refresh_button)

        # 横向排列：标签 + 输入控件
        com_layout.addWidget(com_label)
        com_layout.addLayout(com_input_layout)
        com_layout.addStretch()  # 添加弹性空间

        layout.addWidget(com_container)

        return widget

    def create_hand_params_widget(self):
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: #fafbfc;
                border: 1px solid #e8eaed;
                border-radius: 12px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)

        # 灵巧手标题
        hand_title = QLabel("🖐️ 灵巧手连接参数")
        hand_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #FF6B6B;
                border: none;
                background: transparent;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(hand_title)

        # COM口选择 - 横向布局
        com_container = QWidget()
        com_container.setStyleSheet("QWidget { background: transparent; }")
        com_layout = QHBoxLayout(com_container)  # 改为横向布局
        com_layout.setContentsMargins(0, 0, 0, 0)
        com_layout.setSpacing(15)

        com_label = QLabel("端口号:")
        com_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                background: transparent;
                min-width: 80px;
            }
        """)

        com_input_layout = QHBoxLayout()
        com_input_layout.setSpacing(5)

        self.com_combo = QComboBox()
        self.com_combo.setStyleSheet("""
            QComboBox {
                padding: 12px 16px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                background: white;
                font-size: 14px;
                min-height: 20px;
                min-width: 200px;
            }
            QComboBox::item:selected {
                color: black;
                background-color: #FF6B6B;
            }
        
            QComboBox:focus {
                border-color: #FF6B6B;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6b7280;
                margin-right: 10px;
            }
        """)
        self.refresh_com_ports()

        refresh_button = QPushButton()
        refresh_button.setFixedSize(44, 44)
        refresh_button.setIcon(QIcon(os.path.join(APP_ROOT_PATH, "material", "refresh.png")))
        refresh_button.setIconSize(QSize(32, 32))
        refresh_button.setStyleSheet("""
            QPushButton {
                background: #FF6B6B;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 28px;
            }
            QPushButton:hover {
                background: #FF5252;
            }
            QPushButton:pressed {
                background: #E53935;
            }
        """)
        refresh_button.clicked.connect(self.refresh_com_ports)

        com_input_layout.addWidget(self.com_combo)
        com_input_layout.addWidget(refresh_button)

        # 横向排列：标签 + 输入控件
        com_layout.addWidget(com_label)
        com_layout.addLayout(com_input_layout)
        com_layout.addStretch()  # 添加弹性空间

        layout.addWidget(com_container)

        # Slave ID输入 - 横向布局
        slave_container = QWidget()
        slave_container.setStyleSheet("QWidget { background: transparent; }")
        slave_layout = QHBoxLayout(slave_container)  # 改为横向布局
        slave_layout.setContentsMargins(0, 0, 0, 0)
        slave_layout.setSpacing(15)

        slave_label = QLabel("SLAVE_ID:")
        slave_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                background: transparent;
                min-width: 80px;
            }
        """)

        self.slave_id_input = QSpinBox()
        self.slave_id_input.setRange(1, 255)
        self.slave_id_input.setButtonSymbols(QAbstractSpinBox.NoButtons)
        self.slave_id_input.setStyleSheet("""
             QSpinBox {
                padding: 12px 16px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                background: white;
                font-size: 14px;
                min-height: 20px;
                min-width: 200px;
            }
            QSpinBox:focus {
                border-color: #FF6B6B;
                outline: none;
            }
        """)

        # 横向排列：标签 + 输入框
        slave_layout.addWidget(slave_label)
        slave_layout.addWidget(self.slave_id_input)
        slave_layout.addStretch()  # 添加弹性空间

        layout.addWidget(slave_container)

        return widget

    def create_arm_params_widget(self):
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: #f0f9ff;
                border: 1px solid #e0f2fe;
                border-radius: 12px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)

        # 机械臂标题
        arm_title = QLabel("🦾 机械臂连接参数")
        arm_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #4ECDC4;
                background: transparent;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(arm_title)

        # IP地址输入 - 横向布局
        ip_container = QWidget()
        ip_container.setStyleSheet("QWidget { background: transparent; }")
        ip_layout = QHBoxLayout(ip_container)  # 改为横向布局
        ip_layout.setContentsMargins(0, 0, 0, 0)
        ip_layout.setSpacing(15)

        ip_label = QLabel("IP地址:")
        ip_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                background: transparent;
                min-width: 80px;
            }
        """)

        self.ip_input = QLineEdit()
        self.ip_input.setPlaceholderText("请输入机械臂IP地址 (如: *************)")
        self.ip_input.setStyleSheet("""
            QLineEdit {
                padding: 12px 16px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                background: white;
                font-size: 14px;
                min-height: 20px;
                min-width: 250px;
            }
            QLineEdit:focus {
                border-color: #4ECDC4;
                outline: none;
            }
        """)
        self.ip_input.setText(get_config("REM_ROBOT.ip"))
        # 横向排列：标签 + 输入框
        ip_layout.addWidget(ip_label)
        ip_layout.addWidget(self.ip_input)
        ip_layout.addStretch()  # 添加弹性空间
        layout.addWidget(ip_container)

        # H
        hand_type_container = QWidget()
        hand_type_container.setStyleSheet("QWidget { background: transparent; }")
        hand_type_layout = QHBoxLayout(hand_type_container)  # 改为横向布局
        hand_type_layout.setContentsMargins(0, 0, 0, 0)
        hand_type_layout.setSpacing(15)
        hand_type_label = QLabel("机械臂类型:")
        hand_type_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                background: transparent;
                min-width: 80px;
            }
        """)
        self.hand_type_input = QComboBox()
        self.hand_type_input.setStyleSheet("""
            QComboBox {
                padding: 12px 16px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                background: white;
                font-size: 14px;
                min-height: 20px;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #FF8F00;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::item:selected {
                color: black;
                background-color: #FF6B6B;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6b7280;
                margin-right: 10px;
            }
        """)
        self.hand_type_input.addItems(["Left", "Right"])
        self.hand_type_input.setCurrentText(get_config("REM_ROBOT.hand_side_type"))
        hand_type_layout.addWidget(hand_type_label)
        hand_type_layout.addWidget(self.hand_type_input)
        hand_type_layout.addStretch()  # 添加弹性空间

        layout.addWidget(hand_type_container)

        return widget

    def create_button_section(self, parent_layout):
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)

        # 添加弹性空间
        button_layout.addStretch()

        # 取消按钮
        self.cancel_button = QPushButton("❌ 取消")
        self.cancel_button.setFixedSize(150, 50)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a6268;
            }
            QPushButton:pressed {
                background: #495057;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)  # 关闭对话框

        # 连接按钮
        self.connect_button = QPushButton("🚀 建立连接")
        self.connect_button.setFixedSize(200, 50)
        self.connect_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5a6fd8, stop:1 #6a4190);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4e5bc6, stop:1 #5e377e);
            }
        """)
        self.connect_button.clicked.connect(self.establish_connection)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.connect_button)
        button_layout.addStretch()

        parent_layout.addWidget(button_widget)

    def refresh_com_ports(self):
        """刷新COM口列表"""
        self.com_combo.clear()
        ports = serial_ports()
        for port in ports:
            if not is_armband_device(port):
                self.com_combo.addItem(port)

        if not ports:
            self.com_combo.addItem("未检测到COM口")

    def refresh_armband_com_ports(self):
        self.armband_com_combo.clear()

        ports = serial.tools.list_ports.comports()
        armband_ports = []

        # 筛选臂环设备
        for port in ports:
            if is_armband_device(port):
                armband_ports.append(port)
                self.armband_com_combo.addItem(format_port_info(port))

        # if not armband_ports:
        #     self.armband_com_combo.addItem("未检测到臂环设备COM口")
        #     if ports:
        #         self.armband_com_combo.addItem("--- 所有可用COM口 ---")
        #         for port in ports:
        #             self.armband_com_combo.addItem(format_port_info(port))

    def update_connection_params(self):
        # 臂环参数区域始终显示
        self.armband_params_widget.setVisible(True)

        # 隐藏其他参数控件
        self.hand_params_widget.setVisible(False)
        self.arm_params_widget.setVisible(False)

        mode = getattr(self, 'current_mode', ControlMode.HAND)

        if mode ==  ControlMode.HAND:
            # 手控模式：显示臂环和灵巧手参数
            self.hand_params_widget.setVisible(True)
            self.params_title.setText("🖐️ 连接参数配置")

        elif mode ==  ControlMode.ARM:
            # 臂控模式：显示臂环和机械臂参数
            self.arm_params_widget.setVisible(True)
            self.params_title.setText("🦾 连接参数配置")

        elif mode ==  ControlMode.ARM_HAND:
            # 臂带手模式：显示臂环和机械臂参数（通过机械臂控制灵巧手）
            self.arm_params_widget.setVisible(True)
            self.params_title.setText("🤖 连接参数配置")

        elif mode == ControlMode.COOPERATIVE:
            # 协同控制模式：显示所有参数
            self.arm_params_widget.setVisible(True)
            self.params_title.setText("⚡ 连接参数配置")

    def get_selected_mode(self):
        return getattr(self, 'current_mode', ControlMode.HAND)

    def validate_connection_params(self):
        mode = self.get_selected_mode()

        # 验证臂环参数（所有模式都需要）
        if self.armband_com_combo.currentText() == "未检测到COM口" or not self.armband_com_combo.currentText():
            QMessageBox.warning(self, "参数错误", "请选择有效的臂环COM口")
            return False

        if mode in [ControlMode.HAND]:
            # 需要验证灵巧手参数
            if self.com_combo.currentText() == "未检测到COM口" or not self.com_combo.currentText():
                QMessageBox.warning(self, "参数错误", "请选择有效的灵巧手COM口")
                return False

            # 检查灵巧手COM口和臂环COM口不能相同
            hand_com_text = self.com_combo.currentText()
            hand_com_port = hand_com_text.split(' - ')[0] if ' - ' in hand_com_text else hand_com_text
            armband_com_text = self.armband_com_combo.currentText()
            armband_com_port = armband_com_text.split(' - ')[0] if ' - ' in armband_com_text else armband_com_text

            if hand_com_port == armband_com_port:
                QMessageBox.warning(self, "参数错误", "灵巧手COM口和臂环COM口不能相同，请选择不同的COM口")
                return False

        if mode in [ControlMode.ARM, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
            # 需要验证机械臂参数
            if not self.ip_input.text().strip():
                QMessageBox.warning(self, "参数错误", "请输入机械臂IP地址")
                return False

            # 简单的IP地址格式验证
            ip = self.ip_input.text().strip()
            parts = ip.split('.')
            if len(parts) != 4:
                QMessageBox.warning(self, "参数错误", "IP地址格式不正确")
                return False

            try:
                for part in parts:
                    num = int(part)
                    if num < 0 or num > 255:
                        raise ValueError
            except ValueError:
                QMessageBox.warning(self, "参数错误", "IP地址格式不正确")
                return False

        return True

    def establish_connection(self):
        if not self.validate_connection_params():
            return

        mode = self.get_selected_mode()
        params = {}

        # 收集臂环连接参数（所有模式都需要）
        armband_com_text = self.armband_com_combo.currentText()
        armband_com_port = armband_com_text.split(' - ')[0] if ' - ' in armband_com_text else armband_com_text
        params['armband_com_port'] = armband_com_port

        # 收集其他连接参数
        if mode in [ControlMode.HAND]:
            com_text = self.com_combo.currentText()
            com_port = com_text.split(' - ')[0] if ' - ' in com_text else com_text
            params['hand_com_port'] = com_port
            params['slave_id'] = self.slave_id_input.value()

        if mode in [ControlMode.ARM, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
            params['ip_address'] = self.ip_input.text().strip()
            params['hand_type'] = self.hand_type_input.currentText()

        self.start_connection_test(mode, params)

    def start_connection_test(self, mode, params):
        # 创建进度对话框
        self.progress_dialog = QProgressDialog("正在测试设备连接...", "取消", 0, 0, self)
        self.progress_dialog.setWindowTitle("连接测试")
        self.progress_dialog.setModal(True)
        self.progress_dialog.setMinimumDuration(0)
        self.progress_dialog.setValue(0)

        # 创建并启动测试线程
        self.test_thread = ConnectionTestThread(mode, params)
        self.test_thread.test_completed.connect(self.on_connection_test_completed)
        self.test_thread.progress_updated.connect(self.on_progress_updated)

        # 连接取消按钮
        self.progress_dialog.canceled.connect(self.on_test_canceled)

        # 保存参数供后续使用
        self.current_mode = mode
        self.current_params = params

        # 启动测试
        self.test_thread.start()
        self.progress_dialog.show()

    def on_progress_updated(self, message):
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setLabelText(message)

    def on_test_canceled(self):
        if hasattr(self, 'test_thread') and self.test_thread.isRunning():
            self.test_thread.terminate()
            self.test_thread.wait()

    def on_connection_test_completed(self, success, error_message):
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.close()

        if success:
            mode_names = {
                ControlMode.HAND: "手控模式",
                ControlMode.ARM: "臂控模式",
                ControlMode.ARM_HAND: "臂带手模式",
                ControlMode.COOPERATIVE: "协同控制模式"
            }

            info_text = f"连接模式: {mode_names[self.current_mode]}\n"
            info_text += f"臂环COM口: {self.current_params['armband_com_port']}\n"
            if 'hand_com_port' in self.current_params:
                info_text += f"灵巧手COM口: {self.current_params['hand_com_port']}\n"
                info_text += f"Slave ID: {self.current_params['slave_id']}\n"
            if 'ip_address' in self.current_params:
                info_text += f"机械臂IP地址: {self.current_params['ip_address']}\n"
                info_text += f"机械臂类型: {self.current_params['hand_type']}\n"

            QMessageBox.information(self, "连接成功", f"设备连接测试成功！\n\n{info_text}")

            # 发送连接建立信号
            self.connection_established.emit(self.current_mode, self.current_params)
            self.accept()
        else:
            # 连接失败，显示错误信息
            QMessageBox.critical(self, "连接失败", f"设备连接测试失败！\n\n{error_message}\n\n请检查设备连接和参数配置。")