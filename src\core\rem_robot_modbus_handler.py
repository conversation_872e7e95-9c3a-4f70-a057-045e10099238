import enum
import time

import numpy as np

from robotic_arm_package.robotic_arm import *


def check_master(func):
    def wrapper(self, *args, **kwargs):
        if not self.arm:
            raise Exception('Arm not found, please open it first')
        return func(self, *args, **kwargs)
    return wrapper


class REMRobotModbusHandler(object):
    def __init__(self, hand_side_type: str):
        super().__init__()
        self.arm = None

        self._slave_id = 1  # 无用， 为了和ModbusRtuHandler保持一致
        self._on_error_callback = None
        self._init_arm_control = True

        self.hand_side_type = hand_side_type
        self._is_connected = False

        self.initial_arm_pose = None
        self.initial_euler_data = None

        self.euler_change_buffer = []

        self.emg_calibration_done = False
        self.emg_rest_data = None
        self.emg_rest_rms = None
        self.emg_rest_rms_buffer = []
        self.emg_rest_rms_buffer_avg = 0

    def check_connection(self):
        if isinstance(self.arm, Arm):
            return self._is_connected
        return False

    def set_error_callback(self, callback):
        self._on_error_callback = callback

    def open(self, ip: str, port=1, baudrate=115200, timeout=5):

        try:
            self.arm = Arm(RM65, ip)
            self.arm.Close_Modbus_Mode(port=port, block=True)
            self.arm.Set_Modbus_Mode(port=port, baudrate=baudrate, timeout=timeout, block=True)
            if self.hand_side_type == 'Left':
                self.arm.Write_Single_Register(port=1, address=901, data=2, device=1, block=True)  # 2-LeftHand
            if self.hand_side_type == 'Right':
                self.arm.Write_Single_Register(port=1, address=901, data=1, device=1, block=True)  # 1-RightHand

            self._is_connected = True
        except Exception as e:
            raise Exception('open modbus error: ' + str(e))

    def close(self):
        try:
            if self.arm:
                self.arm.Arm_Socket_Close()
        except Exception as e:
            raise Exception('close modbus error: ' + str(e))
        finally:
            self._is_connected = False
            self.arm = None

    def finger_control(self, finger_data, control_mode):
        print(f"finger_control 开始: {finger_data}, {control_mode}")
        single_data = [0] * 12
        for finger in range(len(finger_data)):
            single_data[finger * 2 + 1] = int(finger_data[finger])

        try:
            if control_mode == 'pos':
                result = self.arm.Write_Registers(port=1, address=1010, num=6, single_data=single_data, device=1, block=False)  # 改为非阻塞
                print(f"finger_control pos 结果: {result}")
            elif control_mode == 'speed':
                result = self.arm.Write_Registers(port=1, address=1016, num=6, single_data=single_data, device=1, block=False)  # 改为非阻塞
                print(f"finger_control speed 结果: {result}")
        except Exception as e:
            print(f"finger_control 异常: {e}")
            raise

    def arm_control_initialization(self, euler_data: np.ndarray):
        # set arm initial pose
        print(1111111111)
        self.arm.Movej_Cmd(joint=[0, 39, 128, 0, -78, 0], v=10, r=0, trajectory_connect=0, block=True)
        print(22222222222)
        # save initial arm pose
        self.initial_arm_pose = self.arm.Get_Current_Arm_State()[2]
        # save initial euler_data
        self.initial_euler_data = euler_data.copy()

    def _arm_control(self, euler_data: np.ndarray, emg_data: np.ndarray):

        # # calculate emg rest data for calibration
        # if not self.emg_calibration_done:
        #     if self.emg_rest_data is None:
        #         self.emg_rest_data = emg_data.copy()
        #         print("\n\nEMG Calibrating\n\n")
        #     else:
        #         self.emg_rest_data = np.concatenate((self.emg_rest_data, emg_data), axis=1)
        #     if len(self.emg_rest_data[0]) >= 500:
        #         self.emg_rest_rms = np.sqrt(np.mean(np.square(self.emg_rest_data), axis=1))
        #         self.emg_calibration_done = True
        #         print("\n\nEMG Calibration Done\n\n")
        #
        # else:
            # arm control
            # 记录多次肌电信号rms的buffer，如果平均值大于X，判定为非静息态，笔抬起
            # self.emg_rest_rms_buffer.append(max(np.sqrt(np.mean(np.square(emg_data), axis=1)) / self.emg_rest_rms))
            # if len(self.emg_rest_rms_buffer) >= 10:
            #     self.emg_rest_rms_buffer = self.emg_rest_rms_buffer[1:]
            #     if np.average(self.emg_rest_rms_buffer) > 2 >= self.emg_rest_rms_buffer_avg:
            #         self.emg_rest_rms_buffer_avg = np.average(self.emg_rest_rms_buffer)
            #         # arm move up
            #         print("\n\n机械臂抬起\n\n")
            #         self.initial_arm_pose[0] += 0.01
            #     elif np.average(self.emg_rest_rms_buffer) <= 2 < self.emg_rest_rms_buffer_avg:
            #         self.emg_rest_rms_buffer_avg = np.average(self.emg_rest_rms_buffer)
            #         # arm move down
            #         print("\n\n机械臂放下\n\n")
            #         self.initial_arm_pose[0] -= 0.01
            #     else:
        # arm move on a plane
        print("机械臂平面运动")
        euler_change = euler_data - self.initial_euler_data
        for index, euler_change_ in enumerate(euler_change):
            if euler_change_ > 180:
                euler_change[index] = euler_change_ - 360
            elif euler_change_ < -180:
                euler_change[index] = euler_change_ + 360

        print(f"euler_data: {euler_data}  euler_change: {euler_change}")

        # self.euler_change_buffer.append(euler_change)
        # if len(self.euler_change_buffer) > 1:
        #     self.euler_change_buffer = self.euler_change_buffer[1:]
        #     euler_change_avg = np.mean(self.euler_change_buffer, axis=0)

        new_arm_pose = self.initial_arm_pose.copy()
        new_arm_pose[1] += euler_change[2] / 200
        new_arm_pose[2] += euler_change[0] / 200
        new_arm_pose[0] += euler_change[1] / 200

        self.arm.Movep_CANFD(pose=new_arm_pose, follow=False)

    @check_master
    def arm_control(self, values: list, emg_values=None):
        if self._init_arm_control:
            self.arm_control_initialization(np.array(values))
            self._init_arm_control = False

        if emg_values is None:
            emg_values = [[] for _ in range(8)]

        self._arm_control(np.array(values), np.array(emg_values))

