#!/bin/bash
set -e -o pipefail

shell_folder=$(dirname "$0")
cd $shell_folder/../proto

output_dir='../generated'

c=(c_out protobuf-c)
cpp=(cpp_out cpp)
nanopb=(nanopb_out nanopb)
python=(python_out python)
dart=(dart_out dart)

plugins=(python)

generate() {
    echo "generate $1 $2"
    for f in *.proto; do
        o=$output_dir/$2
        if [[ ! -e $o ]]; then
            mkdir -p $o
        fi
        protoc -I . --$1=$output_dir/$2 $f
    done
    for f in */*.proto; do
        o=$output_dir/$2/${f%/*}
        if [[ ! -e $o ]]; then
            echo mkdir $o
            mkdir -p $o
        fi
        protoc -I . -I app_dongle -I app_sensor -I dongle_sensor --$1=$o ${f##*/}
    done
}

generate_all() {
    for i in ${plugins[@]}; do
        eval eval generate \${$i[@]}
    done
}

if [[ -z "$1" ]]; then
    generate_all
else
    eval eval generate \${$1[@]}
fi