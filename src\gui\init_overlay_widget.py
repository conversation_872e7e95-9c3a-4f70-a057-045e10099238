from PySide6.Qt<PERSON>ore import Signal, Qt, Q<PERSON><PERSON>ty<PERSON>nimation, QEasingCurve, QTimer
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QScrollArea, QProgressBar, QHBoxLayout, QSizePolicy


class InitializationTask:
    """初始化任务类"""
    def __init__(self, name: str):
        self.name = name
        self.status = "pending"  # pending, running, completed, failed
        self.error_message = ""


class InitializationOverlayWidget(QWidget):
    initializationCompleted = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("InitializationOverlay")
        self.setAttribute(Qt.WA_StyledBackground, True)
        self.setStyleSheet("""
            #InitializationOverlay {
                background-color: rgba(0, 0, 0, 50);
            }
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
            QProgressBar {
                border: 2px solid #2E3034;
                border-radius: 5px;
                text-align: center;
                background-color: #22262E;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 5px;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #3E4044;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #5E6064;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #6E7074;
            }
        """)

        self._tasks = []  # 存储所有注册的任务
        self._task_widgets = {}  # 存储任务对应的UI组件
        self._completed_count = 0
        self._failed_count = 0
        self._init_ui()

    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setAlignment(Qt.AlignCenter)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_widget.setFixedSize(500, 400)
        content_widget.setStyleSheet("background-color: #2E3034; border-radius: 10px;")

        # 标题
        title_label = QLabel(self.tr("初始化软件运行环境..."))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; margin-bottom: 20px;")

        content_layout.addSpacing(5)
        content_layout.addWidget(title_label)
        content_layout.addSpacing(10)

        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setFixedHeight(250)

        # 任务列表容器
        self.tasks_container = QWidget()
        self.tasks_layout = QVBoxLayout(self.tasks_container)
        self.tasks_layout.setContentsMargins(10, 10, 10, 10)
        self.tasks_layout.setSpacing(10)

        self.scroll_area.setWidget(self.tasks_container)
        content_layout.addWidget(self.scroll_area)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(10)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)

        content_layout.addSpacing(20)
        content_layout.addWidget(self.progress_bar)
        content_layout.addStretch()

        main_layout.addWidget(content_widget, 0, Qt.AlignCenter)

    def register_task(self, name: str) -> str:
        task = InitializationTask(name)
        self._tasks.append(task)

        # 创建任务UI
        self._create_task_widget(task)

        # 更新进度条范围
        self.progress_bar.setRange(0, len(self._tasks))

        return name  # 使用任务名作为ID

    def _create_task_widget(self, task: InitializationTask):
        """为任务创建UI组件"""
        # 创建一个容器widget来包装任务布局
        task_widget = QWidget()
        task_layout = QHBoxLayout(task_widget)
        task_layout.setContentsMargins(5, 5, 5, 5)

        # 任务名称
        task_label = QLabel(task.name)
        task_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # 状态图标
        task_status = QLabel("⏳")
        task_status.setFixedWidth(30)

        task_layout.addWidget(task_label)
        task_layout.addWidget(task_status)

        self.tasks_layout.addWidget(task_widget)
        self._task_widgets[task.name] = {
            'task_label': task_label,
            'status_label': task_status,
            'widget': task_widget  # 保存widget引用用于滚动
        }

    def complete_task(self, task_name: str):
        task = self._find_task(task_name)
        if not task:
            return

        if task.status != "completed":
            task.status = "completed"
            self._completed_count += 1

            widgets = self._task_widgets.get(task_name)
            if widgets:
                widgets['status_label'].setText("✅")
                # 滚动到当前完成的任务
                self._scroll_to_task(task_name)

            self._update_progress()

        for task in self._tasks:
            if task.status != "completed":
                return

        self.initializationCompleted.emit()

    def fail_task(self, task_name: str, error_message: str = ""):
        """标记任务失败"""
        task = self._find_task(task_name)
        if not task:
            return

        if task.status != "failed":
            task.status = "failed"
            task.error_message = error_message
            self._failed_count += 1

            widgets = self._task_widgets.get(task_name)
            if widgets:
                widgets['status_label'].setText("❌")
                # 滚动到当前失败的任务
                self._scroll_to_task(task_name)

            self._update_progress()

    def _find_task(self, task_name: str) -> InitializationTask:
        """查找任务"""
        for task in self._tasks:
            if task.name == task_name:
                return task
        return None

    def _scroll_to_task(self, task_name: str):
        widgets = self._task_widgets.get(task_name)
        if not widgets or 'widget' not in widgets:
            return

        task_widget = widgets['widget']
        scroll_bar = self.scroll_area.verticalScrollBar()

        # 获取任务widget在容器中的位置
        widget_pos = task_widget.pos()
        viewport_height = self.scroll_area.viewport().height()

        # 计算目标滚动位置：让任务显示在可视区域的上1/3处
        target_y = widget_pos.y() - viewport_height // 3
        target_y = max(0, min(target_y, scroll_bar.maximum()))

        # 如果已经在可视范围内且位置合适，就不需要滚动
        current_value = scroll_bar.value()
        if abs(current_value - target_y) < 20:  # 20像素的容差
            return

        # 停止之前的动画（如果有）
        if hasattr(self, '_scroll_animation'):
            self._scroll_animation.stop()

        # 创建平滑滚动动画
        self._scroll_animation = QPropertyAnimation(scroll_bar, b"value")
        self._scroll_animation.setDuration(300)  # 300ms动画时间
        self._scroll_animation.setStartValue(current_value)
        self._scroll_animation.setEndValue(target_y)
        self._scroll_animation.setEasingCurve(QEasingCurve.OutCubic)  # 平滑的缓动曲线
        self._scroll_animation.start()

    def _update_progress(self):
        if not self._tasks:
            return

        completed_and_failed = self._completed_count + self._failed_count
        progress = int((completed_and_failed / len(self._tasks)) * 100)
        self.progress_bar.setValue(progress)

    def clear_tasks(self):
        self._tasks.clear()
        self._task_widgets.clear()
        self._completed_count = 0
        self._failed_count = 0

        # 清空UI
        for i in reversed(range(self.tasks_layout.count())):
            item = self.tasks_layout.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None)

        self.progress_bar.setValue(0)

    # 保持向后兼容的方法
    def mark_task_completed(self, task_index_or_name):
        """向后兼容方法"""
        if isinstance(task_index_or_name, int):
            # 旧的索引方式
            if 0 <= task_index_or_name < len(self._tasks):
                task_name = self._tasks[task_index_or_name].name
                self.complete_task(task_name)
        else:
            # 新的名称方式
            self.complete_task(task_index_or_name)