# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'intuitive_action_groupbox.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QGridLayout, QGroupBox,
    QPushButton, QSizePolicy, QSpacerItem, QWidget)

class Ui_ActionForm(object):
    def setupUi(self, ActionForm):
        if not ActionForm.objectName():
            ActionForm.setObjectName(u"ActionForm")
        ActionForm.resize(255, 92)
        ActionForm.setStyleSheet(u"*{\n"
"	font: 11pt;\n"
"}")
        self.gridLayout_2 = QGridLayout(ActionForm)
        self.gridLayout_2.setSpacing(0)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.groupBox = QGroupBox(ActionForm)
        self.groupBox.setObjectName(u"groupBox")
        self.gridLayout = QGridLayout(self.groupBox)
        self.gridLayout.setObjectName(u"gridLayout")
        self.frame = QFrame(self.groupBox)
        self.frame.setObjectName(u"frame")
        self.frame.setFrameShape(QFrame.StyledPanel)
        self.frame.setFrameShadow(QFrame.Raised)

        self.gridLayout.addWidget(self.frame, 0, 0, 1, 2)

        self.add_pushbutton = QPushButton(self.groupBox)
        self.add_pushbutton.setObjectName(u"add_pushbutton")

        self.gridLayout.addWidget(self.add_pushbutton, 1, 0, 1, 1)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.gridLayout.addItem(self.horizontalSpacer, 1, 1, 1, 1)


        self.gridLayout_2.addWidget(self.groupBox, 0, 0, 1, 1)


        self.retranslateUi(ActionForm)

        QMetaObject.connectSlotsByName(ActionForm)
    # setupUi

    def retranslateUi(self, ActionForm):
        ActionForm.setWindowTitle(QCoreApplication.translate("ActionForm", u"Form", None))
        self.groupBox.setTitle("")
        self.add_pushbutton.setText(QCoreApplication.translate("ActionForm", u"add action", None))
    # retranslateUi

