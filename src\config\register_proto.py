
"""

This script automatically loads all the modules in the specified path and lists the classes in the modules.


"""

import importlib
import os
import pkgutil
import sys

_MODULES = {}
_CLASSES = {}
_CLASSES_TYPE = {}
_PACK_NAME_MAP = {}


def create_init_file(folder_path):
    init_file = os.path.join(folder_path, "__init__.py")
    if not os.path.exists(init_file):
        with open(init_file, 'w') as f:
            pass  # 创建一个空的 __init__.py 文件


def _reload_all(proto_path):
    global _MODULES
    global _CLASSES
    global _CLASSES_TYPE
    _MODULES = {}
    _CLASSES = {}
    _CLASSES_TYPE = {}

    # 由于某些协议文件是多文件夹结构, 但是没有为每一个文件夹提升为模块(添加__.init__.py)， 导致 pkgutil.walk_packages
    # 解决方式
    # 1. 生成协议的python文件夹如果涉及到多文件, 必须加上__init__.py

    for root, dirs, files in os.walk(proto_path):
        # 为每个子文件夹创建 __init__.py 文件
        if '__init__.py' not in files:
            create_init_file(root)

    for importer, modname, ispkg in pkgutil.walk_packages(path=[proto_path], onerror=ImportError):
        if not ispkg:
            pack = os.path.split(importer.path)[-1]
            mod = importlib.import_module(modname)
            _MODULES.setdefault(pack, [])
            _MODULES[pack].append(mod)
        else:
            importlib.import_module(modname)

    for pack in _MODULES:
        for mod in _MODULES[pack]:
            mod_scope = [name for name in dir(mod) if not name.startswith('__')]
            for obj in mod_scope:
                mod_attr = getattr(mod, obj)
                if isinstance(mod_attr, type) and not mod_attr.__subclasses__():
                    pack_name = pack[0].upper() + pack[1:]
                    _CLASSES.setdefault(pack_name, [])
                    if mod_attr.__name__ not in _CLASSES[pack_name]:
                        _CLASSES[pack_name].append(mod_attr.__name__)
                        _CLASSES_TYPE[mod_attr.__name__] = mod_attr


def register_proto_class(proto_path):
    sys.path.append(proto_path)

    # 递归添加所有proto文件夹
    for root, dirs, files in os.walk(proto_path):
        for _dir in dirs:
            sys.path.append(os.path.join(root, _dir))

    if not _MODULES:
        if os.path.exists(proto_path):
            _reload_all(proto_path)


def get_proto_class(name):
    if name in _CLASSES_TYPE:
        return _CLASSES_TYPE[name]
    raise NameError("{} is not a proto class".format(name))


def list_proto_class():
    return _CLASSES.copy()


if __name__ == '__main__':
    # 导入文件夹下py脚本内的所有类
    folder_path = ""

    sys.path.append(folder_path)
    # 递归添加所有文件
    for root, dirs, files in os.walk(folder_path):
        for _dir in dirs:
            sys.path.append(os.path.join(root, _dir))

    register_proto_class(folder_path)

    # 列出所有类
    print(list_proto_class())

    # 获取类
    print(get_proto_class("DeviceStatus"))
