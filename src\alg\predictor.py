import time

import numpy as np
import os
from scipy import signal

start_import_time = time.time()
from keras import utils as np_utils
from keras.src.callbacks import EarlyStopping
from keras.src.optimizers import Adam
from keras.src.models import Model
from keras.src.layers import Dense, Activation, Dropout
from keras.src.layers import Conv2D, AveragePooling2D
from keras.src.layers import BatchNormalization
from keras.src.layers import Input, Flatten
from keras.src.saving import load_model

print("predictor import cost: ", time.time() - start_import_time)


class ArmbandPredictor(object):
    fs = 250  # 采样频率
    n_channel = 8  # 电极通道数
    input_length = 60  # 输入数据长度
    gesture_classes = None  # 手势种类

    x_train = None
    x_test = None
    y_test = None
    y_train = None

    model = None

    def __init__(self):
        self._enable = False

    @property
    def enable(self):
        return self._enable

    def _data_preprocess(self, folder_name):
        file_names = os.listdir(folder_name)

        x_train = []
        x_validate = []
        y_train = []
        y_validate = []
        for index, file_name in enumerate(file_names, start=1):
            first_underline_index = file_name.find("_")
            start_index = first_underline_index + 1
            second_underline_index = file_name.find("_", first_underline_index + 1)
            action_index = int(file_name[start_index:second_underline_index])
            last_underline_index = file_name.rfind("_")
            run_index = int(file_name[last_underline_index + 1:file_name.rfind(".")])

            raw_data = []
            # 将txt文件转换为array
            with open(folder_name + '/' + file_name, 'r') as f:
                data_lists = f.readlines()
                for data_list in data_lists:
                    data = eval(data_list)['afe_data']
                    if not raw_data:
                        raw_data = data.copy()
                    else:
                        for i in range(len(raw_data)):
                            data1 = raw_data[i].copy()
                            data1.extend(data[i])
                            raw_data[i] = data1.copy()

            raw_data = np.array(raw_data)

            if run_index == 0:
                pos = 0
                while pos < len(raw_data[0]) - self.input_length:
                    x_validate.append(raw_data[:, pos:pos + self.input_length])
                    pos = pos + 3
                    y_validate.append(action_index)
            else:
                pos = 0
                while pos < len(raw_data[0]) - self.input_length:
                    x_train.append(raw_data[:, pos:pos + self.input_length])
                    pos = pos + 3
                    y_train.append(action_index)

        self.gesture_classes = len(set(y_train))

        x_train = np.array(x_train)
        x_validate = np.array(x_validate)
        y_train = np.array(y_train)
        y_validate = np.array(y_validate)

        x_train = filtering(x_train, self.fs)
        x_validate = filtering(x_validate, self.fs)

        self.x_train = x_train
        self.x_validate = x_validate
        self.y_train = y_train
        self.y_validate = y_validate

    def training(self, folder_name, output_path, description=""):
        self._enable = False
        print("folder_name: ", folder_name)
        print("output_path: ", output_path)
        start_time = time.time()

        self._data_preprocess(folder_name)

        unique_labels = sorted(set(self.y_train))
        output_size = max(unique_labels) + 1

        X_train = np.abs(self.x_train).copy()
        Y_train = self.y_train.copy()
        X_validate = np.abs(self.x_validate).copy()
        Y_validate = self.y_validate.copy()

        Y_train = np_utils.to_categorical(Y_train, output_size)
        Y_validate = np_utils.to_categorical(Y_validate, output_size)

        X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], X_train.shape[2], 1)
        X_validate = X_validate.reshape(X_validate.shape[0], X_validate.shape[1], X_validate.shape[2], 1)

        model = create_custom_model(self.input_length, output_size, unique_labels, description)
        # print(model.summary())

        # compile the model and set the optimizers
        opt = Adam()
        model.compile(loss='categorical_crossentropy', optimizer=opt, metrics=['accuracy'])

        callback = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)

        model.fit(X_train, Y_train, batch_size=int(X_train.shape[0] * 0.2), epochs=200,
                  verbose=2, validation_data=(X_validate, Y_validate), callbacks=[callback])
        model.save(output_path)

        print(f'***************************** cost: {time.time() - start_time} s *****************************')
        print(f'***************************** 训练结束  *****************************')

    def loading_model(self, model_path):
        start_time = time.time()

        self.model = load_model(model_path, custom_objects={"CustomModel": CustomModel})
        print("Extra Info:", self.model.extra_info)
        self._enable = True

        print(f'***************************** cost: {time.time() - start_time} s *****************************')
        print(f'***************************** 加载结束  *****************************')

    def predict(self, realtime_data):
        realtime_data = filtering(realtime_data, self.fs)
        realtime_data = np.abs(realtime_data).copy()
        realtime_data = realtime_data.reshape(1, realtime_data.shape[0], realtime_data.shape[1], 1)
        probs = self.model.predict(realtime_data)

        valid_labels = self.model.extra_info['parameters']['valid_labels']
        valid_probs = np.array([probs[0][label] for label in valid_labels])
        predicted_label = valid_labels[np.argmax(valid_probs)]
        print(f"predicted: {predicted_label}")
        return predicted_label


def notch_filter(data, f0, fs):
    Q = 30
    b, a = signal.iirnotch(f0, Q, fs)
    y = signal.filtfilt(b, a, data)

    return y


def butter_bandpass_filter(data, lowcut, highcut, fs, order=5):
    nyq = fs * 0.5  # 奈奎斯特采样频率
    low, high = lowcut / nyq, highcut / nyq
    sos = signal.butter(order, [low, high], analog=False, btype='band', output='sos')
    y = signal.sosfiltfilt(sos, data)

    return y


def filtering(data, fs):
    # # 信号预处理
    # 1、带通滤波
    f_low = 20  # low frequency
    f_high = 120  # high frequency
    data = butter_bandpass_filter(data, f_low, f_high, fs)
    # 2、循环陷波滤波
    for f0 in np.arange(np.ceil(f_low / 50) * 50, f_high + 1, 50):
        # f0: Frequency to be removed from signal (Hz)
        data = notch_filter(data, int(f0), fs)

    return data


class CustomModel(Model):
    def __init__(self, inputs, outputs, extra_info=None, **kwargs):
        super().__init__(inputs=inputs, outputs=outputs, **kwargs)
        self.extra_info = extra_info or {}

    def get_config(self):
        base_config = super().get_config()
        custom_config = {
            "extra_info": self.extra_info
        }
        return {**base_config, **custom_config}

    @classmethod
    def from_config(cls, config):
        extra_info = config.pop('extra_info', {})

        # 从extra_info的parameters中获取参数
        parameters = extra_info.get('parameters', {})
        n_classes = parameters.get('n_classes', 2)  # 从parameters中获取n_classes
        Samples = parameters.get('Samples', 60)  # 从parameters中获取Samples

        inputs = Input(shape=(8, Samples, 1))
        block = Conv2D(8, (2, 2), input_shape=(8, Samples, 1))(inputs)
        block = BatchNormalization()(block)
        block = Activation('relu')(block)
        block = AveragePooling2D((1, 2))(block)
        block = Dropout(0.5)(block)

        dense = Flatten()(block)
        dense = Dense(n_classes)(dense)
        outputs = Activation('softmax')(dense)

        model = cls(inputs=inputs, outputs=outputs, extra_info=extra_info, **config)
        return model


def create_custom_model(Samples, n_classes, valid_labels, description):
    inputs = Input(shape=(8, Samples, 1))
    block = Conv2D(8, (2, 2), input_shape=(8, Samples, 1))(inputs)
    block = BatchNormalization()(block)
    block = Activation('relu')(block)
    block = AveragePooling2D((1, 2))(block)
    block = Dropout(0.5)(block)

    dense = Flatten()(block)
    dense = Dense(n_classes)(dense)
    outputs = Activation('softmax')(dense)

    extra_info = {
        "description": description,
        "parameters": {
            "Samples": Samples,
            "n_classes": n_classes,
            "valid_labels": valid_labels
        }
    }

    model = CustomModel(inputs=inputs, outputs=outputs, extra_info=extra_info)
    return model


if __name__ == '__main__':
    folder_name = r"E:/armband/sample"
    action = ArmbandPredictor()
    action.training(folder_name, 'model.h5')

    # action.loading_model('model.h5')
    #
    # # prediction simulate
    # file_names = os.listdir(folder_name)
    #
    # x_train = []
    # x_test = []
    # y_train = []
    # y_test = []
    # for index, file_name in enumerate(file_names, start=1):
    #     first_underline_index = file_name.find("_")
    #     start_index = first_underline_index + 1
    #     second_underline_index = file_name.find("_", first_underline_index + 1)
    #     action_index = int(file_name[start_index:second_underline_index])
    #     last_underline_index = file_name.rfind("_")
    #     run_index = int(file_name[last_underline_index + 1:file_name.rfind(".")])
    #
    #     if run_index == 3:
    #         raw_data = []
    #         # 将txt文件转换为array
    #         with open(folder_name + '/' + file_name, 'r') as f:
    #             data_lists = f.readlines()
    #             for data_list in data_lists:
    #                 data = eval(data_list)['afe_data']
    #                 if not raw_data:
    #                     raw_data = data.copy()
    #                 else:
    #                     for i in range(len(raw_data)):
    #                         data1 = raw_data[i].copy()
    #                         data1.extend(data[i])
    #                         raw_data[i] = data1.copy()
    #
    #         raw_data = np.array(raw_data)
    #
    #         pos = 0
    #         while pos < len(raw_data[0]) - 60:
    #             x_test.append(raw_data[:, pos:pos + 60])
    #             pos = pos + 3
    #             y_test.append(action_index)
    #
    # x_test = np.array(x_test)
    # y_test = np.array(y_test)
    #
    # for i, x in enumerate(x_test):
    #     action.predict(x)
    #     # print(f"y_label: {y_test[i]}")