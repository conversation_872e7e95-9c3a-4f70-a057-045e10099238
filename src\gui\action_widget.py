from PySide6.QtWidgets import QWidget, QGridLayout, QSizePolicy, QPushButton
from PySide6.QtCore import Signal, QPropertyAnimation, QEvent, QTimer

from .ui import action_form, intuitive_action_form, intuitive_action_groupbox
from ..utils.config_loader import get_config


class BaseWidget(QWidget):
    def __init__(self):
        super(BaseWidget, self).__init__()

    def changeEvent(self, evt):
        if evt.type() == QEvent.LanguageChange:
            self.retranslateUi(self)
        super(BaseWidget, self).changeEvent(evt)


# 单指
class IntuitiveActionWidget(BaseWidget, intuitive_action_form.Ui_ActionForm):
    start_signal = Signal(str)
    delete_signal = Signal(str)
    timeout_signal = Signal(str)
    _progressbar_animation = None

    def __init__(self, action_name):
        super(IntuitiveActionWidget, self).__init__()
        self.setupUi(self)
        self._action_name = action_name
        self._init_ui()

        self.remaining_time = 3
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_countdown)  # 定时触发

    def update_countdown(self):
        if self.remaining_time > 0:
            self.start_pushbutton.setText(str(self.remaining_time))
            self.remaining_time -= 1
        else:
            self.remaining_time = 3
            self.timer.stop()
            self.start_pushbutton.setText(self._action_name.split(".")[0])
            self._start()

    def _init_ui(self):
        self.start_pushbutton.setText(self._action_name.split(".")[0])
        self.start_pushbutton.clicked.connect(self._on_clicked_start_button)
        self.delete_pushbutton.clicked.connect(self._on_clicked_delete_button)
        self.progressBar.setValue(0)
        self._progressbar_animation = QPropertyAnimation(self.progressBar, b'value')
        self._progressbar_animation.finished.connect(self._on_progressbar_reach_its_end)
        self._progressbar_animation.setDuration(get_config("ACTION_PARAMS_CONFIG.duration_s") * 1000)
        self._progressbar_animation.setStartValue(0)
        self._progressbar_animation.setEndValue(100)

    def _start(self):
        self.start_signal.emit(self._action_name)
        self._progressbar_animation.start()

    def _on_clicked_start_button(self):
        self.start_pushbutton.setEnabled(False)
        self.start_pushbutton.setText("准备采集")
        self.timer.start(1000)

    def _on_clicked_delete_button(self):
        self.delete_signal.emit(self._action_name)
        self.hide()

    def _on_progressbar_reach_its_end(self):
        self.timeout_signal.emit(self._action_name)
        self.start_pushbutton.setEnabled(True)


class IntuitiveActionGroupbox(BaseWidget, intuitive_action_groupbox.Ui_ActionForm):
    start_signal = Signal(str)
    delete_signal = Signal(str)
    timeout_signal = Signal(str)

    def __init__(self, class_index, config):
        super(IntuitiveActionGroupbox, self).__init__()
        self.setupUi(self)
        self._action_widgets = []
        self._recorded_files = []
        self.class_index = class_index
        self.class_config = config
        self.class_name = self.class_config["name"]
        self._init_ui()

    def recorded_actions(self):
        return self._recorded_files

    def _init_ui(self):
        self.groupBox.setTitle(self.class_name)
        self.frame.setLayout(QGridLayout())
        self.frame.layout().setContentsMargins(0, 0, 0, 0)
        self.frame.layout().setSpacing(0)
        self.add_pushbutton.clicked.connect(self._add_action_widget)
        for i in range(3):
            self._add_action_widget()
        self.add_pushbutton.setText("添加样本")

    def _add_action_widget(self):
        index = len(self._action_widgets)
        widget = IntuitiveActionWidget("{}_{}_{}.txt".format(self.class_name, self.class_index, index))
        widget.start_signal.connect(self._on_clicked_start)
        widget.delete_signal.connect(self._on_clicked_delete)
        widget.timeout_signal.connect(self._on_action_timeout)
        self.frame.layout().addWidget(widget)
        self._action_widgets.append(widget)

    def _on_clicked_start(self, action_name):
        self.start_signal.emit(action_name)

    def _on_clicked_delete(self, action_name):
        if action_name in self._recorded_files:
            self._recorded_files.remove(action_name)
        self.delete_signal.emit(action_name)

    def _on_action_timeout(self, action_name):
        if action_name not in self._recorded_files:
            self._recorded_files.append(action_name)
        self.timeout_signal.emit(action_name)
