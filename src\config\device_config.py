# 臂环设备的VID/PID配置
ARMBAND_DEVICE_CONFIG = {
    # 设备名称: (VID, PID, 描述)
    "ARMBAND_DEVICE": (None, 0x0005, "Armband Device"),  # 臂环设备的特定PID
}

# 从配置中提取VID/PID列表
ARMBAND_VID_PID_LIST = [(vid, pid) for vid, pid, _ in ARMBAND_DEVICE_CONFIG.values()]


def is_armband_device(port):
    # 检查PID是否为臂环设备的特定PID (0x0005)
    if hasattr(port, 'pid') and port.pid is not None:
        if port.pid == 0x0005:
            return True

    return False


def format_port_info(port):
    base_info = f"{port.device} - {port.description}"
    
    if hasattr(port, 'vid') and hasattr(port, 'pid') and port.vid is not None and port.pid is not None:
        vid_pid_info = f" (VID:{port.vid:04X} PID:{port.pid:04X})"
        return base_info + vid_pid_info
    
    return base_info
