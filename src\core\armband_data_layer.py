# -*- coding: utf-8 -*-
import asyncio
import datetime
import time
from threading import Thread

from ahrs.filters import <PERSON><PERSON><PERSON>
from ruamel.yaml import YAML
from scipy.spatial.transform import Rotation

from src.config.config import ControlMode
from src.utils.config_loader import get_config, SINGLE_FINGER_CONFIG, GRIPS_CONFIG, GRIPS_ID_MAP
from src.utils.data_handler import ParseNode, NodeData, ToolProtoDataManageHandler
from src.utils.filter_sdk import B<PERSON>BandStopFilter, BWBandPassFilter, BWLowPassFilter, BWHighPassFilter
from src.utils.public_funcs import trim_data, save_data_to_file
from src.utils.tool_packet import ToolPacket

yaml = YAML()

import enum
import numpy as np
from PySide6.QtCore import QObject, Signal
from loguru import logger

from src.config.register_proto import get_proto_class
# from src.config.settings import BASIC, FILTER_<PERSON>NFIG
# from src.config.stark_modbus_protocol import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RegisterAddress
# from src.core.data_handler import ParseNode, NodeData
# from src.core.data_transfer_node import BWBandPassFilter, BWBandStopFilter, BWHighPassFilter, BWLowPassFilter
# from src.core.tool_packet import ToolPacket
# from src.utils.public_func import trim_data, save_data_to_file, calculate_fft, check_received_sn
#
# from scipy.spatial.transform import Rotation
# from ahrs.filters import Mahony
#
# OTA_PACK_SIZE = 512
# OTA_MAX_RETRY = 5
# OTA_RETRY_TIME_S = 2
#
#
# WINDOW_IN_SECOND = BASIC["window_in_seconds"]


class AfeData(object):
    def __init__(self, seq_num: int, sample_rate: int, lead_off_bits: int, afe_data: list):
        self.seq_num = seq_num
        self.sample_rate = sample_rate
        self.afe_data = afe_data
        self.lead_off_bits = lead_off_bits


class ArmbandDataLayer(QObject):

    current_emg_file = ""
    euler_calibration_done_signal = Signal()

    def __init__(self):
        super().__init__()
        self.data_manage_handler = ToolProtoDataManageHandler()

        self.acc_coefficient = 0.0001220703125
        self.gyro_coefficient = 0.06103515625
        self.mag_coefficient = 0.00152587890625

        self.finger_predictor = None
        self._finger_target_id = 0
        self._arm_target_position = None

        self.finger_predictor_enable = True

        self.mag_data_list = [[], [], []]
        self.orientation = None
        self.q = None
        self.st = 0
        self.ed = 0

        self.euler_calibration_done = False
        self.euler_calibration_start_time = None
        self.euler_data_buffer = []
        self.euler_calibration_count = 0

        self._current_mode = ControlMode.HAND

        self._afe_data_list = None
        self._afe_filters = []
        self._afe_sample_rate = 250
        self._channel_count = 8
        self._initialize_filters(self._afe_sample_rate, self._channel_count)

        self._imu_data_type = "calibrated"   # raw  calibrated

        # 初始化缓冲区
        self._buffer_size = self._afe_sample_rate * 3  # 5秒的数据
        self._afe_buffer = None
        self._afe_filter_buffer = None

        # device info
        # parse_node = ParseNode(["SensorApp", "device_info"], ["sensor_type", "fw_version", "sensor_features"])
        # self.data_manage_handler.register_parsed_callback_func(parse_node, self._on_device_info_data)

    @property
    def current_mode(self):
        return self._current_mode

    def set_control_mode(self, mode: ControlMode):
        self._current_mode = mode

        parse_node = ParseNode(["SensorApp", "afe_data"], ["seq_num", "lead_off_bits", "channel_adc_value"])
        self.data_manage_handler.register_parsed_callback_func(parse_node, self._on_afe_data_callback)

        if mode in [ControlMode.ARM, ControlMode.COOPERATIVE]:
            self._register_imu_parse_node()

        if self._current_mode == ControlMode.ARM:
            self.finger_predictor_enable = False

        # if mode in [ControlMode.HAND, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
        #     parse_node = ParseNode(["SensorApp", "afe_data"], ["seq_num", "lead_off_bits", "channel_adc_value"])
        #     self.data_manage_handler.register_parsed_callback_func(parse_node, self._on_afe_data_callback)

    def _register_imu_parse_node(self):
        # imu
        parse_node = ParseNode(["SensorApp", "imu_data"], ["seq_num", "acc_raw_data", "gyro_raw_data", "eular_raw_data", "acc_correction_data", "gyro_correction_data"])
        self.data_manage_handler.register_parsed_callback_func(parse_node, self._on_imu_data_callback)

        # mag
        parse_node = ParseNode(["SensorApp", "mag_data"], ["seq_num", "mag_raw_data", "mag_correction_data"])
        self.data_manage_handler.register_parsed_callback_func(parse_node, self._on_mag_data_callback)

    def get_armband_command(self, scribed: bool):
        proto_obj = get_proto_class("AppSensor")()
        if scribed:
            # if self._current_mode in [ControlMode.HAND, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
            proto_obj.afe_config.sample_rate = 3
            proto_obj.afe_config.channel_bits = 255

            if self._current_mode in [ControlMode.ARM, ControlMode.COOPERATIVE]:
                proto_obj.imu_config.imu_mode = 3  # ACC_GYRO
                proto_obj.imu_config.imu_sr = 5  # 400Hz
                proto_obj.imu_config.data_type = 1 if self._imu_data_type == "raw" else 2

                proto_obj.mag_config.mag_sr = 5   # MAG_SR_100
                proto_obj.mag_config.data_type = 1 if self._imu_data_type == "raw" else 2
        else:
            # """
            # enum ConfigReqType {
            #       REQ_NONE     = 0;
            #       GET_DEVICE_INFO  = 1;   // 请求设备信息， 设备端上报：DeviceInfo
            #       SET_DEVICE_INFO  = 2;   // 设置设备信息
            #       GET_PORT_STAT    = 3;   // 请求端口状态， <主控盒> 上报
            #       GET_SENSOR_CONFIG = 4;  // 读取所有sensor config
            #       START_DATA_STREAM = 6;  // 按默认配置一键订阅传感器数据
            #       STOP_DATA_STREAM = 7;  // 停止全部订阅数据流
            #
            #     }
            # """
            proto_obj.msg_cmd = 7

        content = ToolPacket(payload=proto_obj.SerializeToString(),
                             source_id=1,
                             destination_id=3)
        print("{}\n{}".format("AppSensor", proto_obj))
        logger.info("{}\n{}".format("AppSensor", proto_obj))
        logger.info("{}".format(content.encode()))
        return content.encode()

    def _on_mag_data_callback(self, node_data: NodeData):
        mag_raw = node_data.get_value("mag_raw_data")
        mag_correction_data = node_data.get_value("mag_correction_data")

        if self._imu_data_type == "raw":
            self.mag_data_list = parse_mag_raw_data(mag_raw, self.mag_coefficient)
        else:
            self.mag_data_list = parse_mag_correction_data(mag_correction_data, self.mag_coefficient)

    def _on_imu_data_callback(self, node_data: NodeData):
        acc_raw_data = node_data.get_value("acc_raw_data")
        gyro_raw_data = node_data.get_value("gyro_raw_data")
        acc_correction_data = node_data.get_value("acc_correction_data")
        gyro_correction_data = node_data.get_value("gyro_correction_data")

        if self._imu_data_type == "raw":
            acc_data_list = parse_imu_data(acc_raw_data, self.acc_coefficient)
            gyro_data_list = parse_imu_data(gyro_raw_data, self.gyro_coefficient)
        else:
            acc_data_list = parse_imu_correction_data(acc_correction_data, self.acc_coefficient)
            gyro_data_list = parse_imu_correction_data(gyro_correction_data, self.gyro_coefficient)

        if self.euler_calibration_start_time is None:
            self.euler_calibration_start_time = time.time()
        self._calculate_euler(acc_data_list, gyro_data_list, self.mag_data_list)

    def _calculate_euler(self, acc_data_list, gyro_data_list, mag_data_list):
        if mag_data_list[0]:
            acc = np.array(acc_data_list)[:, -1] * 9.8  # g -> m/s^2
            gyro = np.radians(np.array(gyro_data_list)[:, -1])
            mag = np.array(mag_data_list)[:, -1] * 100  # gauss -> uT

            if self.orientation is None:
                self.orientation = Mahony(acc=np.array([acc]), gyr=np.array([gyro]), mag=np.array([mag]))
                self.q = self.orientation.Q[-1]
            else:
                self.q = self.orientation.updateMARG(self.q, gyr=gyro, acc=acc, mag=mag)

            euler = Rotation.from_quat(self.q).as_euler('ZYX', degrees=True)

            if self.euler_calibration_done:
                self._arm_target_position = euler  # 下发给机械臂的指令

            # make sure the euler data is calibrated
            if not self.euler_calibration_done:
                # print(f"euler_data: {euler}")
                self.euler_data_buffer.append(euler)
                if len(self.euler_data_buffer) >= 50:
                    self.euler_data_buffer = self.euler_data_buffer[1:]
                    euler_data_buffer_avg = np.mean(self.euler_data_buffer, axis=0)
                    print(max(np.abs(euler - euler_data_buffer_avg)))
                    if max(np.abs(euler - euler_data_buffer_avg)) < 0.01:
                        self.euler_calibration_done = True
                        print("\n\nEuler Calibration Done\n\n")
                        print(f"IMU校准完成，{time.time() - self.euler_calibration_start_time} s")
                        self.euler_calibration_done_signal.emit()

    def _on_afe_data_callback(self, node_data: NodeData):
        seq_num = node_data.get_value("seq_num")
        lead_off_bits = node_data.get_value("lead_off_bits")
        channel_adc_value = node_data.get_value("channel_adc_value")

        afe_data_list = [parse_afe_data(data) for data in channel_adc_value]

        # 原始数据不解析

        if self._afe_buffer is None:
            self._afe_buffer = np.array(afe_data_list, dtype=np.int32)
        else:
            self._afe_buffer = trim_data(
                np.concatenate([self._afe_buffer, afe_data_list], 1),
                1,
                self._buffer_size
            )

        # 滤波处理
        if len(self._afe_filters):
            filtered_data_list = self._apply_filters_simple(afe_data_list)
            self._afe_data_list = filtered_data_list

            # 更新滤波缓冲区
            if self._afe_filter_buffer is None:
                self._afe_filter_buffer = np.array(filtered_data_list, dtype=np.float32)
            else:
                self._afe_filter_buffer = trim_data(
                    np.concatenate([self._afe_filter_buffer, filtered_data_list], 1),
                    1,
                    self._buffer_size
                )

        if self.finger_predictor_enable:
            predict_buffer = trim_data(self._afe_buffer, 1, 60)
            self.start_predict(predict_buffer)

        if self.current_emg_file:
            data_dict = {
                "seq_num": seq_num,
                "sample_rate": self._afe_sample_rate,
                "afe_data": afe_data_list
            }
            save_data_to_file(self.current_emg_file, data_dict)

    def start_predict(self, predict_buffer):
        if self.finger_predictor and self.finger_predictor.enable:
            if predict_buffer.shape[1] == 60:

                try:
                    self._finger_target_id = self.finger_predictor.predict(predict_buffer)
                except Exception as e:
                    logger.error(f"Error in finger predictor: {predict_buffer}")
                    import traceback
                    traceback.print_exc()

    def _apply_filters_simple(self, afe_data_list):
        filtered_data_list = []
        for channel_index, value_list in enumerate(afe_data_list):
            if channel_index < len(self._afe_filters):
                filtered_data = []
                for value in value_list:
                    filtered_value = float(value)
                    for _filter in self._afe_filters[channel_index]:
                        filtered_value = _filter.filter(filtered_value)
                    filtered_data.append(round(filtered_value, 2))
                filtered_data_list.append(filtered_data)
            else:
                # 如果没有对应的滤波器，直接转换为float
                filtered_data_list.append([round(float(v), 2) for v in value_list])
        return filtered_data_list

    def _initialize_filters(self, sample_rate, channel_count):
        self._afe_filters.clear()

        for i in range(channel_count):
            _filter_list = []
            for config_dict in get_config("FILTER"):
                filter_label = list(config_dict.keys())[0]
                config = config_dict[filter_label]
                if filter_label == "bs":
                    _filter = BWBandStopFilter(order=config[0], sample_rate=sample_rate, fl=config[1], fu=config[2])
                elif filter_label == "bp":
                    _filter = BWBandPassFilter(order=config[0], sample_rate=sample_rate, fl=config[1], fu=config[2])
                elif filter_label == "hp":
                    _filter = BWHighPassFilter(order=config[0], sample_rate=sample_rate, f=config[1])
                else:
                    _filter = BWLowPassFilter(order=config[0], sample_rate=sample_rate, f=config[1])

                _filter_list.append(_filter)

            self._afe_filters.append(_filter_list)

    @property
    def afe_data_list(self):
        return self._afe_data_list

    @property
    def finger_target_id(self):
        return self._finger_target_id

    @property
    def arm_target_position(self):
        return self._arm_target_position

    def reset_predict_buffer(self):
        self._finger_target_id = 0

    @property
    def afe_buffer(self):
        return self._afe_buffer

    @property
    def afe_filter_buffer(self):
        return self._afe_filter_buffer

    def get_finer_id_target_positions(self):
        return GRIPS_ID_MAP[self._finger_target_id]["initialPosition"]


def parse_afe_data(data_str):
    new_data = []
    for i in range(0, len(data_str) // 3):
        adc_data = int.from_bytes(data_str[3 * i:3 * i + 3], byteorder='big', signed=True)
        new_data.append(adc_data)
    return new_data


def parse_imu_data(data_bytes, sensitivity):
    data = [[], [], []]
    for i in range(len(data_bytes) // 2):
        col = i % 3
        value = int.from_bytes(data_bytes[i * 2:i * 2 + 2], byteorder='little', signed=True)
        value = value * sensitivity
        data[col].append(value)
    return data


def parse_imu_correction_data(acc_correction_data, sensitivity):
    data = [[], [], []]
    for i in range(len(acc_correction_data)):
        col = i % 3
        value = acc_correction_data[i] * sensitivity
        data[col].append(value)
    return data


def parse_mag_raw_data(data_bytes, sensitivity):
    data = [[], [], []]
    for i in range(len(data_bytes) // 2):
        col = i % 3
        value = int.from_bytes(data_bytes[i * 2:i * 2 + 2], byteorder='little', signed=True)
        value = value * sensitivity
        data[col].append(value)
    return data


def parse_mag_correction_data(mag_correction_data, sensitivity):
    data = [[], [], []]
    for i in range(len(mag_correction_data)):
        col = i % 3
        value = mag_correction_data[i] * sensitivity
        data[col].append(value)
    return data
