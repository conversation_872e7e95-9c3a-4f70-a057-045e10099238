
import enum
import time
import threading
from typing import Optional, Callable, List, Union, Any
from loguru import logger

try:
    from pymodbus.client.serial import ModbusSerialClient
    from pymodbus.exceptions import ModbusException, ConnectionException
    from pymodbus.constants import <PERSON>ian
    from pymodbus.payload import BinaryPayloadDecoder, BinaryPayloadBuilder
    import serial
except ImportError:
    try:
        # Try alternative import for different pymodbus versions
        from pymodbus.client import ModbusSerialClient
        from pymodbus.exceptions import ModbusException, ConnectionException
        from pymodbus.constants import Endian
        from pymodbus.payload import BinaryPayloadDecoder, BinaryPayloadBuilder
        import serial
    except ImportError:
        logger.warning("Modbus communication libraries not available. Install pymodbus and pyserial.")
        ModbusSerialClient = None
        ModbusException = None
        ConnectionException = None
        serial = None


class ModbusDataType(enum.IntEnum):
    """Modbus数据类型枚举"""
    HOLDING_REGISTER = 1    # 保持寄存器
    INPUT_REGISTER = 2      # 输入寄存器
    COIL = 3               # 线圈
    DISCRETE_INPUT = 4     # 离散输入


class ModbusStatus(enum.IntEnum):
    """Modbus连接状态枚举"""
    DISCONNECTED = 0
    CONNECTING = 1
    CONNECTED = 2
    ERROR = 3


def check_modbus_connection(func):
    """检查Modbus连接状态的装饰器"""
    def wrapper(self, *args, **kwargs):
        if not self.is_connected():
            raise Exception('Modbus not connected, please connect first')
        return func(self, *args, **kwargs)
    return wrapper


class ModbusRtuHandler(object):
    """
    通用 Modbus RTU 通信处理器

    使用 pymodbus 库实现的 Modbus RTU 客户端，支持：
    - 串口通信配置
    - 读写保持寄存器
    - 读写输入寄存器
    - 读写线圈
    - 读写离散输入
    - 连接状态管理
    - 错误处理和重连
    """

    def __init__(self, slave_id: int = 1):
        super().__init__()

        # 基本配置
        self._slave_id = slave_id
        self._port = None
        self._baudrate = 9600
        self._timeout = 1.0
        self._parity = 'N'  # None, Even, Odd
        self._stopbits = 1
        self._bytesize = 8

        # 连接状态
        self._status = ModbusStatus.DISCONNECTED
        self._is_connected = False
        self._last_error = None

        # Modbus 客户端
        self._modbus_client = None

        # 回调函数
        self._on_error_callback = None
        self._on_status_change_callback = None

        # 线程控制
        self._thread_lock = threading.Lock()

        logger.info(f"ModbusRtuHandler initialized with slave_id={slave_id}")

    def _set_status(self, new_status: ModbusStatus):
        old_status = self._status
        self._status = new_status

        if self._on_status_change_callback and old_status != new_status:
            try:
                self._on_status_change_callback(old_status, new_status)
            except Exception as e:
                logger.error(f"Error in status change callback: {e}")

    def _handle_error(self, error_message: str, exception: Exception = None):
        self._last_error = error_message
        self._set_status(ModbusStatus.ERROR)

        logger.error(error_message)
        if exception:
            logger.exception(exception)

        if self._on_error_callback:
            try:
                self._on_error_callback(error_message)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")

    def set_callbacks(self, on_error: Callable[[str], None] = None,
                     on_status_change: Callable[[ModbusStatus, ModbusStatus], None] = None):
        """
        设置回调函数

        Args:
            on_error: 错误回调函数
            on_status_change: 状态变化回调函数
        """
        self._on_error_callback = on_error
        self._on_status_change_callback = on_status_change

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._is_connected and self._modbus_client is not None

    def get_status(self) -> ModbusStatus:
        return self._status

    def get_last_error(self) -> Optional[str]:
        return self._last_error

    def open(self, port: str, baudrate: int = 115200, timeout: float = 0.05,
             parity: str = 'N', stopbits: int = 1, bytesize: int = 8) -> bool:
        """
        打开串口连接

        Args:
            port (str): 串口名称 (如 'COM3' 或 '/dev/ttyUSB0')
            baudrate (int): 波特率，默认9600
            timeout (float): 超时时间，默认1.0秒
            parity (str): 校验位 ('N'=None, 'E'=Even, 'O'=Odd)
            stopbits (int): 停止位，默认1
            bytesize (int): 数据位，默认8

        Returns:
            bool: 连接成功返回True，失败返回False
        """
        if self._is_connected:
            logger.warning("Already connected")
            return True

        if not ModbusSerialClient:
            self._handle_error("Modbus communication libraries not available")
            return False

        try:
            self._set_status(ModbusStatus.CONNECTING)

            # 保存连接参数
            self._port = port
            self._baudrate = baudrate
            self._timeout = timeout
            self._parity = parity
            self._stopbits = stopbits
            self._bytesize = bytesize

            # 创建 Modbus 客户端
            self._modbus_client = ModbusSerialClient(port=port,
                                                     baudrate=baudrate,
                                                     timeout=timeout,
                                                     parity=parity,
                                                     stopbits=stopbits,
                                                     bytesize=bytesize)

            # 尝试连接
            if not self._modbus_client.connect():
                self._handle_error(f"Failed to connect to {port}")
                return False

            # 测试连接 - 尝试读取一个寄存器
            self._test_connection()

            self._is_connected = True
            self._set_status(ModbusStatus.CONNECTED)

            logger.info(f"Successfully connected to Modbus device on {port} at {baudrate} baud")
            return True

        except Exception as e:
            self._handle_error(f"Failed to open connection: {str(e)}", e)
            return False

    def _test_connection(self):
        try:
            # 尝试读取一个保持寄存器来测试连接
            result = self._modbus_client.read_holding_registers(address=0, count=1, slave=self._slave_id)
            if result.isError():
                logger.warning(f"Connection test warning: {result}")
            else:
                logger.debug("Connection test successful")
        except Exception as e:
            logger.warning(f"Connection test failed: {e}")

    def close(self):
        try:
            if self._modbus_client:
                self._modbus_client.close()
                logger.info("Modbus connection closed")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")
        finally:
            self._is_connected = False
            self._modbus_client = None
            self._set_status(ModbusStatus.DISCONNECTED)

    def reconnect(self) -> bool:
        if not self._port:
            self._handle_error("No previous connection parameters available")
            return False

        logger.info("Attempting to reconnect...")
        self.close()
        return self.open(self._port, self._baudrate, self._timeout,
                        self._parity, self._stopbits, self._bytesize)

    @check_modbus_connection
    def read_holding_registers(self, address: int, count: int = 1) -> Optional[List[int]]:
        """
        读取保持寄存器

        Args:
            address (int): 起始地址
            count (int): 读取数量

        Returns:
            Optional[List[int]]: 寄存器值列表，失败返回None
        """
        try:
            with self._thread_lock:
                result = self._modbus_client.read_holding_registers(
                    address=address, count=count, slave=self._slave_id
                )

                if result.isError():
                    self._handle_error(f"Failed to read holding registers at {address}: {result}")
                    return None

                return result.registers

        except Exception as e:
            self._handle_error(f"Error reading holding registers: {str(e)}", e)
            return None

    @check_modbus_connection
    def write_holding_register(self, address: int, value: int) -> bool:
        """
        写入单个保持寄存器

        Args:
            address (int): 寄存器地址
            value (int): 要写入的值

        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            with self._thread_lock:
                result = self._modbus_client.write_register(
                    address=address, value=value, slave=self._slave_id
                )

                if result.isError():
                    self._handle_error(f"Failed to write holding register at {address}: {result}")
                    return False

                return True

        except Exception as e:
            self._handle_error(f"Error writing holding register: {str(e)}", e)
            return False

    @check_modbus_connection
    def write_holding_registers(self, address: int, values: List[int]) -> bool:
        """
        写入多个保持寄存器

        Args:
            address (int): 起始地址
            values (List[int]): 要写入的值列表

        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            with self._thread_lock:
                result = self._modbus_client.write_registers(
                    address=address, values=values, slave=self._slave_id
                )

                if result.isError():
                    self._handle_error(f"Failed to write holding registers at {address}: {result}")
                    return False

                return True

        except Exception as e:
            self._handle_error(f"Error writing holding registers: {str(e)}", e)
            return False

    @check_modbus_connection
    def read_input_registers(self, address: int, count: int = 1) -> Optional[List[int]]:
        """
        读取输入寄存器

        Args:
            address (int): 起始地址
            count (int): 读取数量

        Returns:
            Optional[List[int]]: 寄存器值列表，失败返回None
        """
        try:
            with self._thread_lock:
                result = self._modbus_client.read_input_registers(
                    address=address, count=count, slave=self._slave_id
                )

                if result.isError():
                    self._handle_error(f"Failed to read input registers at {address}: {result}")
                    return None

                return result.registers

        except Exception as e:
            self._handle_error(f"Error reading input registers: {str(e)}", e)
            return None

    @check_modbus_connection
    def read_discrete_inputs(self, address: int, count: int = 1) -> Optional[List[bool]]:
        """
        读取离散输入

        Args:
            address (int): 起始地址
            count (int): 读取数量

        Returns:
            Optional[List[bool]]: 离散输入状态列表，失败返回None
        """
        try:
            with self._thread_lock:
                result = self._modbus_client.read_discrete_inputs(
                    address=address, count=count, slave=self._slave_id
                )

                if result.isError():
                    self._handle_error(f"Failed to read discrete inputs at {address}: {result}")
                    return None

                return result.bits[:count]  # 只返回请求的数量

        except Exception as e:
            self._handle_error(f"Error reading discrete inputs: {str(e)}", e)
            return None

    def registers_to_float32(self, registers: List[int], byte_order: str = 'big') -> List[float]:
        """
        将寄存器值转换为32位浮点数

        Args:
            registers (List[int]): 寄存器值列表（每2个寄存器组成一个float32）
            byte_order (str): 字节序 ('big' 或 'little')

        Returns:
            List[float]: 浮点数列表
        """
        if len(registers) % 2 != 0:
            raise ValueError("Register count must be even for float32 conversion")

        floats = []
        endian = Endian.BIG if byte_order == 'big' else Endian.LITTLE

        for i in range(0, len(registers), 2):
            decoder = BinaryPayloadDecoder.fromRegisters(
                registers[i:i+2], byteorder=endian
            )
            floats.append(decoder.decode_32bit_float())

        return floats

    def float32_to_registers(self, values: List[float], byte_order: str = 'big') -> List[int]:
        """
        将32位浮点数转换为寄存器值

        Args:
            values (List[float]): 浮点数列表
            byte_order (str): 字节序 ('big' 或 'little')

        Returns:
            List[int]: 寄存器值列表
        """
        registers = []
        endian = Endian.BIG if byte_order == 'big' else Endian.LITTLE

        for value in values:
            builder = BinaryPayloadBuilder(byteorder=endian)
            builder.add_32bit_float(value)
            registers.extend(builder.to_registers())

        return registers

    def registers_to_int32(self, registers: List[int], byte_order: str = 'big') -> List[int]:
        """
        将寄存器值转换为32位整数

        Args:
            registers (List[int]): 寄存器值列表（每2个寄存器组成一个int32）
            byte_order (str): 字节序 ('big' 或 'little')

        Returns:
            List[int]: 32位整数列表
        """
        if len(registers) % 2 != 0:
            raise ValueError("Register count must be even for int32 conversion")

        integers = []
        endian = Endian.BIG if byte_order == 'big' else Endian.LITTLE

        for i in range(0, len(registers), 2):
            decoder = BinaryPayloadDecoder.fromRegisters(
                registers[i:i+2], byteorder=endian
            )
            integers.append(decoder.decode_32bit_int())

        return integers

    def int32_to_registers(self, values: List[int], byte_order: str = 'big') -> List[int]:
        """
        将32位整数转换为寄存器值

        Args:
            values (List[int]): 32位整数列表
            byte_order (str): 字节序 ('big' 或 'little')

        Returns:
            List[int]: 寄存器值列表
        """
        registers = []
        endian = Endian.BIG if byte_order == 'big' else Endian.LITTLE

        for value in values:
            builder = BinaryPayloadBuilder(byteorder=endian)
            builder.add_32bit_int(value)
            registers.extend(builder.to_registers())

        return registers

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def __del__(self):
        try:
            self.close()
        except:
            pass


def create_modbus_handler(slave_id: int = 1) -> ModbusRtuHandler:
    """
    创建 Modbus RTU 处理器实例的工厂函数

    Args:
        slave_id (int): Modbus 从站ID

    Returns:
        ModbusRtuHandler: 处理器实例
    """
    return ModbusRtuHandler(slave_id=slave_id)


def scan_modbus_devices(ports: List[str] = None,
                       baudrates: List[int] = None,
                       slave_ids: List[int] = None,
                       timeout: float = 0.5) -> List[dict]:
    """
    扫描 Modbus 设备

    Args:
        ports (List[str]): 要扫描的端口列表，None则自动检测
        baudrates (List[int]): 要尝试的波特率列表
        slave_ids (List[int]): 要尝试的从站ID列表
        timeout (float): 连接超时时间

    Returns:
        List[dict]: 发现的设备列表
    """
    if not ModbusSerialClient:
        logger.error("Modbus library not available")
        return []

    if ports is None:
        # 自动检测串口
        try:
            import serial.tools.list_ports
            ports = [port.device for port in serial.tools.list_ports.comports()]
        except ImportError:
            logger.error("Serial tools not available for port detection")
            return []

    if baudrates is None:
        baudrates = [9600, 19200, 57600, 115200, 460800]

    if slave_ids is None:
        slave_ids = list(range(1, 11))

    found_devices = []

    for port in ports:
        for baudrate in baudrates:
            for slave_id in slave_ids:
                try:
                    handler = ModbusRtuHandler(slave_id=slave_id)
                    if handler.open(port, baudrate, timeout):
                        # 尝试读取一个寄存器来验证设备
                        registers = handler.read_holding_registers(100, 1)
                        if registers is not None:
                            found_devices.append({
                                'port': port,
                                'baudrate': baudrate,
                                'slave_id': slave_id,
                                'test_register_value': registers[0] if registers else None
                            })
                            logger.info(f"Found Modbus device on {port} at {baudrate} baud, slave_id={slave_id}")
                        handler.close()
                        break  # 找到设备后跳出从站ID循环
                except Exception as e:
                    logger.debug(f"Failed to connect to {port} at {baudrate}, slave_id={slave_id}: {e}")
                    continue

    logger.info(f"Scan completed. Found {len(found_devices)} Modbus devices")
    return found_devices


if __name__ == '__main__':
    # 示例：创建 Modbus RTU 处理器并读取寄存器
    print(scan_modbus_devices(ports=["COM44"]))
