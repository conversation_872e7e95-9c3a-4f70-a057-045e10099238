import os
import shutil
import time

import serial.tools.list_ports
import numpy as np
import pyqtgraph as pg
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QRadioButton, QGroupBox, QLabel,
                               QLineEdit, QComboBox, QPushButton, QMessageBox,
                               QFrame, QSpacerItem, QSizePolicy, QGridLayout,
                               QButtonGroup, QDialog, QTabWidget, QSlider,
                               QProgressBar, QTextEdit, QSplitter, QStatusBar,
                               QMenuBar, QMenu, QToolBar, QScrollArea, QAbstractScrollArea, QProgressDialog,
                               QFileDialog)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QTimer, QThread
from PySide6.QtGui import QFont, QIcon, QPixmap, QAction
from loguru import logger

from src.config.config import ControlMode
from src.core.armband_data_layer import ArmbandDataLayer
from src.core.modbus_rtu_handler import ModbusRtuHandler
from src.core.rem_robot_modbus_handler import REMRobotModbusHandler
from src.core.serial_handler import SerialCommunication, SerialConfig
from src.core.task_manager import TaskManager
from src.gui.action_widget import IntuitiveActionGroupbox
from src.gui.init_overlay_widget import InitializationOverlayWidget
from src.path import APP_ROOT_PATH
from src.utils.config_loader import SINGLE_FINGER_CONFIG, GRIPS_CONFIG, get_config, save_config
from src.utils.data_handler import ToolProtoDataManageHandler
from version import VERSION

DATA_PATH = "data"
EMG_DATA_FOLDER = "sample"

if os.path.exists(EMG_DATA_FOLDER):
    shutil.rmtree(EMG_DATA_FOLDER)
os.makedirs(EMG_DATA_FOLDER)

if not os.path.exists("models"):
    os.makedirs("models")

FIRST_IMPORT_MODEL = True


class ImportThread(QThread):
    finished = Signal(object)  # 任务完成信号

    def run(self):
        from ..alg.predictor import ArmbandPredictor
        predictor = ArmbandPredictor()
        self.finished.emit(predictor)


class TrainThread(QThread):
    train_finished_signal = Signal(bool, str)

    def __init__(self):
        super().__init__()
        self._predictor = None
        self.model_output_path = ""
        self._sample_files_path = ""
        self._model_description = None

    def run(self):
        try:
            self._predictor.training(self._sample_files_path, self.model_output_path, self._model_description)
            self.train_finished_signal.emit(True, "")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.train_finished_signal.emit(False, str(e))

    def start_train(self, predictor, sample_files_path, model_output_path, model_description):
        self._predictor = predictor
        self.model_output_path = model_output_path
        self._sample_files_path = sample_files_path
        self._model_description = model_description
        self.start()


class GenerateModelWidget(QGroupBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTitle("生成模型")

        self.model_name_lineedit = QLineEdit()
        self.model_name_lineedit.setPlaceholderText("name")
        self.model_desc_lineedit = QLineEdit()
        self.model_desc_lineedit.setPlaceholderText("description")

        self.summary_groupbox = QGroupBox()
        self.summary_groupbox.setTitle("已录入数据")

        v_layout = QVBoxLayout()
        v_layout.addWidget(self.model_name_lineedit)
        v_layout.addWidget(self.model_desc_lineedit)
        # v_layout.addWidget(self.summary_groupbox)

        self.train_button = QPushButton("生成模型")
        self.train_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        h_layout = QHBoxLayout()
        h_layout.addLayout(v_layout)
        h_layout.addWidget(self.train_button)

        self.setLayout(h_layout)


class MainWindow(QMainWindow):
    serial_data_received_signal = Signal(bytes)

    def __init__(self):
        super().__init__()
        self.connection_params = {}

        # 设备控制
        self.serial_handler = None
        self.hand_control_handler = None
        self.arm_control_handler = None

        self.armband_data_layer = ArmbandDataLayer()
        self.task_manager = TaskManager(self.armband_data_layer)

        # 初始化臂环数据相关变量
        self.armband_data = np.zeros((8, 1000))  # 8通道，每通道1000个数据点
        self.data_index = 0
        self.max_data_points = 1000

        self.gesture_action_widgets = []
        self.intuitive_action_widgets = []
        self.gesture_recorded_actions = {}

        # 设置窗口基本属性
        self.setup_window()
        self.create_tool_bar()
        self.create_status_bar()
        self.create_main_interface()

        self.plot_timer = QTimer()
        self.plot_timer.timeout.connect(self.plot_armband_data)

        self.import_tf_env_thread = ImportThread()
        self.model_train_thread = TrainThread()

        self.progress_dialog = QProgressDialog(self)
        self.progress_dialog.setWindowFlags(Qt.Tool | Qt.CustomizeWindowHint | Qt.X11BypassWindowManagerHint)
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setCancelButton(None)
        self.progress_dialog.close()

        # 创建初始化遮罩层
        self.initialization_overlay = InitializationOverlayWidget(self)
        self.initialization_overlay.hide()

        self.connect_event()

    def connect_event(self):
        self.armband_data_layer.euler_calibration_done_signal.connect(self.euler_calibration_done)

        self.serial_data_received_signal.connect(self.armband_data_layer.data_manage_handler.on_received_device_raw_message)

        self.import_tf_env_thread.finished.connect(self.on_import_tf_env_finished)
        self.model_train_thread.train_finished_signal.connect(self._model_train_finished)
        self.model_train_widget.train_button.clicked.connect(self.on_clicked_train_button)

        self.initialization_overlay.initializationCompleted.connect(self._on_initialization_completed)

        self.start_control_button.clicked.connect(self.on_clicked_start_control_button)
        self.imu_calibration_button.clicked.connect(self.on_clicked_imu_calibration_button)

    def euler_calibration_done(self):
        self.start_control_button.setEnabled(True)
        self.imu_calibration_button.setEnabled(False)
        self.imu_calibration_button.setText("臂环校准完成")
        self.imu_calibration_button.setStyleSheet("background-color: gray")

    def _on_initialization_completed(self):
        self.initialization_overlay.hide()
        self.plot_timer.start(200)

    def on_clicked_train_button(self):
        files_name = os.listdir(EMG_DATA_FOLDER)

        if len(files_name) == 0:
            QMessageBox.warning(self, "提示", "样本数量不能为0, 请采集样本")
            return

        gesture_classes = []
        for name in files_name:
            if not self.data_validation(os.path.join(EMG_DATA_FOLDER, name)):
                return
            gesture_id = name.split('_')[1]
            if gesture_id not in gesture_classes:
                gesture_classes.append(gesture_id)

        if len(self.model_train_widget.model_name_lineedit.text()) == 0:
            QMessageBox.warning(self, "提示", "请输入模型名称")
            return

        model_path = os.path.join(APP_ROOT_PATH, "models", self.model_train_widget.model_name_lineedit.text() + ".h5")
        if os.path.exists(model_path):
            replay = QMessageBox.question(self, "询问", "模型已存在, 是否覆盖?")
            if replay == QMessageBox.StandardButton.No:
                return

        self.setEnabled(False)
        self.task_manager.control_enable = False
        self._set_start_control_button_style(False)
        self.progress_dialog.setLabelText("正在训练模型...")
        self.progress_dialog.setValue(99)
        self.progress_dialog.show()
        self.model_train_thread.start_train(self.armband_data_layer.finger_predictor, EMG_DATA_FOLDER, model_path, self.model_train_widget.model_desc_lineedit.text())

    def _model_train_finished(self, success, msg):
        self.setEnabled(True)
        self.progress_dialog.close()
        if success:
            try:
                self._load_model(file_path=self.model_train_thread.model_output_path)
                model_name = os.path.basename(self.model_train_thread.model_output_path)
                self.model_status_label.setText(f"{model_name} 模型加载成功|")
                self.model_status_label.setStyleSheet("color: green; font-weight: bold;")
            except Exception as e:
                QMessageBox.information(self, "提示", f"加载训练模型失败: {e}")
                return
            QMessageBox.information(self, "提示", "训练模型成功")
        else:
            QMessageBox.information(self, "提示", f"训练模型失败: {msg}")

    def on_import_tf_env_finished(self, predictor):
        self.import_tf_env_thread.deleteLater()
        self.armband_data_layer.finger_predictor = predictor

        self.initialization_overlay.complete_task("导入模型环境")

        if self.armband_data_layer.current_mode in [ControlMode.HAND, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
            model_name = get_config("MODEL_CONFIG").get("name", "")
            default_model_path = os.path.join(APP_ROOT_PATH, "models", str(model_name))
            if os.path.exists(default_model_path):
                self._load_model(file_path=default_model_path, enable_save_config=False)
                self.model_status_label.setText(f"模型加载成功|")
                self.model_status_label.setToolTip(f"模型路径: {default_model_path}")
                self.model_status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.model_status_label.setText("模型未导入, 请导入模型或训练新模型|")
                self.model_status_label.setStyleSheet("color: red; font-weight: bold;")

        self.initialization_overlay.complete_task("加载默认模型")

    def _load_model(self, file_path=None, enable_save_config=True):
        if file_path is None:
            file_path = QFileDialog.getOpenFileName(self, "选择文件", "models", "*.h5")[0]

        if os.path.exists(file_path):
            self.armband_data_layer.finger_predictor.loading_model(file_path)
            self.model_status_label.setText(f"模型加载成功|")
            self.model_status_label.setToolTip(f"模型路径: {file_path}")
            self.model_status_label.setStyleSheet("color: green; font-weight: bold;")
            if enable_save_config:
                # write config
                with open(get_config("SETTINGS_FILE_PATH"), 'w', encoding='utf-8') as f:
                    config = get_config("ALL")
                    config["MODEL_CONFIG"]["name"] = os.path.basename(file_path)
                    save_config(config)

    def init_predictor_env(self):
        if self.armband_data_layer.current_mode in [ControlMode.ARM, ControlMode.COOPERATIVE]:
            self.imu_calibration_button.setVisible(True)
            self.imu_calibration_button.setEnabled(True)
            self.start_control_button.setEnabled(False)
        else:
            self.imu_calibration_button.setVisible(False)
            self.start_control_button.setEnabled(True)

        if self.armband_data_layer.current_mode != ControlMode.ARM:
            # 清空之前的任务并注册新任务
            self.initialization_overlay.clear_tasks()

            self.initialization_overlay.register_task("导入模型环境")
            self.initialization_overlay.register_task("加载默认模型")
            self.import_tf_env_thread.start()

            # 显示遮罩层
            self.initialization_overlay.setGeometry(self.rect())
            self.initialization_overlay.show()
            self.initialization_overlay.raise_()
        else:
            self.load_model_btn.setEnabled(False)  # 臂控模式不需要加载模型
            self.plot_timer.start(200)  # 若不需要遮罩层则直接打开绘图定时器

    def setup_window(self):
        self.setWindowTitle("臂环灵巧手遥操作控制系统" + "  " + VERSION)
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
        """)

    def create_tool_bar(self):
        toolbar = self.addToolBar('主工具栏')
        toolbar.setStyleSheet("""
            QToolBar {
                background: #495057;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QToolButton {
                background: transparent;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-size: 12px;
            }
            QToolButton:hover {
                background: #6c757d;
            }
            QToolButton:pressed {
                background: #5a6268;
            }
        """)

        # 重新下发读取臂环数据指令
        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self._load_model)
        toolbar.addWidget(self.load_model_btn)

        armband_btn = QPushButton("订阅设备数据")
        armband_btn.clicked.connect(lambda: self._send_armband_command(True))
        toolbar.addWidget(armband_btn)

    def _send_armband_command(self, subscribed=True):
        self.serial_handler.write(self.armband_data_layer.get_armband_command(subscribed))

    def create_status_bar(self):
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: #343a40;
                color: white;
                border-top: 1px solid #495057;
            }
        """)

        self.status_widget = QWidget()
        self.status_widget.setLayout(QHBoxLayout())

        self.model_status_label = QLabel()
        self.status_widget.layout().addWidget(self.model_status_label)
        self.status_bar.addWidget(self.status_widget)

    def create_main_interface(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧臂环数据显示面板
        self.create_armband_panel(splitter)

        # 右侧手势训练面板
        self.create_right_panel(splitter)

        # 设置分割器比例 (左侧臂环数据占更大比例)
        splitter.setSizes([900, 500])

    def create_armband_panel(self, parent):
        armband_widget = QWidget()
        armband_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 10px;
                border: 1px solid #dee2e6;
            }
        """)

        armband_layout = QVBoxLayout(armband_widget)
        armband_layout.setContentsMargins(15, 15, 15, 15)
        armband_layout.setSpacing(10)

        # 标题
        title_label = QLabel("臂环8通道数据实时显示")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #343a40;
                padding-bottom: 10px;
                border-bottom: 2px solid #e9ecef;
            }
        """)
        armband_layout.addWidget(title_label)

        # 创建pyqtgraph绘图区域
        self.create_armband_plots(armband_layout)

        parent.addWidget(armband_widget)

    def create_armband_plots(self, parent_layout):
        # 设置pyqtgraph背景为白色
        pg.setConfigOption('background', 'w')
        pg.setConfigOption('foreground', 'k')

        # 创建绘图窗口
        self.plot_widget = pg.GraphicsLayoutWidget()
        self.plot_widget.setStyleSheet("""
            QWidget {
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)

        # 通道颜色配置
        colors = [
            '#FF6B6B',  # 红色
            '#4ECDC4',  # 青色
            '#45B7D1',  # 蓝色
            '#96CEB4',  # 绿色
            '#FFEAA7',  # 黄色
            '#DDA0DD',  # 紫色
            '#98D8C8',  # 薄荷绿
            '#F7DC6F'   # 金黄色
        ]

        # 创建8个子图
        self.plots = []
        self.curves = []

        for i in range(8):
            # 创建子图
            plot = self.plot_widget.addPlot(row=i//2, col=i%2)
            plot.setLabel('left', f'通道 {i+1}')
            plot.showGrid(x=True, y=True, alpha=0.3)
            plot.enableAutoRange(enable=True)

            # 创建曲线
            curve = plot.plot(pen=pg.mkPen(color=colors[i], width=1.5))

            self.plots.append(plot)
            self.curves.append(curve)

        parent_layout.addWidget(self.plot_widget)

    def create_right_panel(self, parent):
        self.action_widget = QWidget()
        parent.addWidget(self.action_widget)

        self.action_widget.setFixedWidth(400)
        action_widget_layout = QVBoxLayout()
        self.action_widget.setLayout(action_widget_layout)
        self.action_tab_widget = QTabWidget()
        self.logo_label = QLabel()
        self.logo_label.setFixedHeight(150)
        self.logo_label.setScaledContents(True)
        pix_img = QPixmap()
        pix_img.load(os.path.join(APP_ROOT_PATH, "material", "logo.png"))
        self.logo_label.setPixmap(pix_img)

        self._init_grip_config_widget()

        self.model_train_widget = GenerateModelWidget()

        self.start_control_button = QPushButton("开始遥操作")
        self.start_control_button.setStyleSheet("background-color: gray")
        self.start_control_button.setFixedHeight(50)

        self.imu_calibration_button = QPushButton("臂环校准(请保持臂环静止)")
        self.imu_calibration_button.setStyleSheet("background-color: yellow")
        self.imu_calibration_button.setFixedHeight(50)

        action_widget_layout.addWidget(self.logo_label)
        action_widget_layout.addWidget(self.action_tab_widget)
        action_widget_layout.addWidget(self.model_train_widget)
        action_widget_layout.addWidget(self.imu_calibration_button)
        action_widget_layout.addWidget(self.start_control_button)
        action_widget_layout.addStretch()

    def _init_grip_config_widget(self, delete_existing_action_file=None):
        # 先清空action_tab_widget的widget
        self.action_tab_widget.clear()
        self.intuitive_action_widgets.clear()
        self.gesture_action_widgets.clear()

        # 单指训练
        single_finger_widget = QWidget()
        single_finger_widget.setLayout(QVBoxLayout())
        self.action_tab_widget.addTab(single_finger_widget, "单指训练")
        area_widget = QScrollArea()
        area_widget.setWidgetResizable(True)
        area_widget.setFrameShape(QFrame.NoFrame)
        area_widget.setSizeAdjustPolicy(QAbstractScrollArea.SizeAdjustPolicy.AdjustToContents)
        single_finger_widget.layout().addWidget(area_widget)
        single_finger_groupbox = QGroupBox()
        single_finger_groupbox.setLayout(QVBoxLayout())
        area_widget.setWidget(single_finger_groupbox)
        for i in SINGLE_FINGER_CONFIG:
            widget = IntuitiveActionGroupbox(i["id"], i)
            widget.start_signal.connect(self.on_intuitive_action_data_start_signal)
            widget.delete_signal.connect(delete_existing_action_file)
            widget.timeout_signal.connect(self.on_intuitive_action_timeout_signal)
            single_finger_groupbox.layout().addWidget(widget)
            self.intuitive_action_widgets.append(widget)

        # 手势训练
        gesture_widget = QWidget()
        gesture_widget.setLayout(QVBoxLayout())
        self.action_tab_widget.addTab(gesture_widget, "手势训练")
        area_widget = QScrollArea()
        area_widget.setWidgetResizable(True)
        area_widget.setFrameShape(QFrame.NoFrame)
        area_widget.setSizeAdjustPolicy(QAbstractScrollArea.SizeAdjustPolicy.AdjustToContents)
        gesture_widget.layout().addWidget(area_widget)
        gesture_groupbox = QGroupBox()
        gesture_groupbox.setLayout(QVBoxLayout())
        area_widget.setWidget(gesture_groupbox)
        for i in GRIPS_CONFIG:
            widget = IntuitiveActionGroupbox(i["id"], i)
            widget.start_signal.connect(self.on_intuitive_action_data_start_signal)
            widget.delete_signal.connect(delete_existing_action_file)
            widget.timeout_signal.connect(self.on_intuitive_action_timeout_signal)
            gesture_groupbox.layout().addWidget(widget)
            self.gesture_action_widgets.append(widget)

    def on_clicked_imu_calibration_button(self):
        self.imu_calibration_button.setText("臂环校准中...")
        self.imu_calibration_button.setEnabled(False)
        self._send_armband_command(True)

    def on_clicked_start_control_button(self):
        if self.armband_data_layer.current_mode != ControlMode.ARM:
            if self.armband_data_layer.finger_predictor and not self.armband_data_layer.finger_predictor.enable:
                QMessageBox.information(self, "提示", "请先加载模型")
                return

        self.task_manager.control_enable = not self.task_manager.control_enable
        self._set_start_control_button_style( self.task_manager.control_enable)

    def _set_start_control_button_style(self, started):
        if not started:
            self.start_control_button.setText("开始遥操作")
            self.start_control_button.setStyleSheet("background-color: gray")
        else:
            self.start_control_button.setText("停止遥操作")
            self.start_control_button.setStyleSheet("background-color: yellow")

    def on_intuitive_action_data_start_signal(self, action_name):
        self.start_data_logging(action_name)

    def on_intuitive_action_timeout_signal(self, action_name):
        file_name = self.armband_data_layer.current_emg_file
        self.stop_data_logging(action_name)
        self.data_validation(file_name)

    def start_data_logging(self, file_name):
        self.armband_data_layer.current_emg_file = os.path.join(EMG_DATA_FOLDER, file_name)
        logger.info("{} start recording...".format(file_name))
        with open(self.armband_data_layer.current_emg_file, 'w+', newline="") as f:
            pass
        self.action_tab_widget.setEnabled(False)

    def stop_data_logging(self, file_name):
        logger.info("{} stopped.".format(file_name))
        self.armband_data_layer.current_emg_file = ""
        self.action_tab_widget.setEnabled(True)

    def data_validation(self, file_name):
        if os.path.isfile(file_name):
            file_size = int(os.path.getsize(os.path.join(file_name)) // 1024)
            if file_size <= get_config("ACTION_PARAMS_CONFIG.minimum_file_kb"):
                QMessageBox.warning(self, "Error", "{}\n文件数据量异常（{}KB）, \n请重新录入！".format(file_name, file_size))
                return False
            else:
                return True
        return False

    def on_connection_established(self, mode, params):
        self.armband_data_layer.set_control_mode(mode)
        self.connection_params = params

        # 初始化臂环数据读取类
        serial_config = SerialConfig()
        serial_config.port = params["armband_com_port"]

        # 臂环数据读取
        self.serial_handler = SerialCommunication(serial_config)
        self.serial_handler.on_data_received = self.serial_data_received_signal.emit
        self.serial_handler.connect()
        if self.serial_handler.is_connected():
            self._send_armband_command(False)

        if self.armband_data_layer.current_mode in [ControlMode.HAND]:
            # 灵巧手控制
            self.hand_control_handler = ModbusRtuHandler(params["slave_id"])
            self.hand_control_handler.open(port=params["hand_com_port"])

        if mode in [ControlMode.ARM, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
            self.arm_control_handler = REMRobotModbusHandler(params["hand_type"])
            self.arm_control_handler.open(params["ip_address"])

            if mode == ControlMode.ARM:
                self.model_train_widget.hide()
                self.action_tab_widget.hide()

        self.task_manager.set_control_mode(mode)
        self.task_manager.set_handlers(self.hand_control_handler, self.arm_control_handler)

        # 更新界面状态
        self.update_connection_status()

    def update_connection_status(self):
        if self.serial_handler:
            connection_info = "臂环已连接|" if self.serial_handler.is_connected() else "臂环未连接"
            label = QLabel(connection_info)
            color = "green" if self.serial_handler.is_connected() else "red"
            label.setStyleSheet("color: {}; font-weight: bold;".format(color))
            self.status_widget.layout().insertWidget(0, label)
        if self.armband_data_layer.current_mode in [ControlMode.ARM, ControlMode.ARM_HAND, ControlMode.COOPERATIVE]:
            connection_info = "机械臂已连接|" if self.arm_control_handler.check_connection() else "机械臂未连接|"
            label = QLabel(connection_info)
            color = "green" if self.serial_handler.is_connected() else "red"
            label.setStyleSheet("color: {}; font-weight: bold;".format(color))
            self.status_widget.layout().insertWidget(0, label)

    def close_devices(self):
        if self.serial_handler:
            self.serial_handler.disconnect()

        if self.hand_control_handler:
            self.hand_control_handler.close()

        if self.arm_control_handler:
            self.arm_control_handler.close()

    def plot_armband_data(self):
        filtered_buffer_data = self.armband_data_layer.afe_filter_buffer
        if filtered_buffer_data is not None:
            for idx, data_list in enumerate(filtered_buffer_data):
                self.curves[idx].setData(data_list)

    def closeEvent(self, event):
        self.task_manager.stop_control_loop()  # 安全关闭控制线程
        self._send_armband_command(False)
        self.plot_timer.stop()
        self.close_devices()
        event.accept()
