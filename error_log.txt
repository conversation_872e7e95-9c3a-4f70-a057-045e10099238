
--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 528, in on_intuitive_action_data_start_signal
    self.start_data_logging(action_name)
  File "E:\armband\src\gui\main_window.py", line 538, in start_data_logging
    with open(self.armband_data_layer.current_emg_file, 'w+', newline="") as f:
FileNotFoundError: [Errno 2] No such file or directory: 'sample\\放松_0_0.txt'

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 616, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 606, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 606, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 609, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\main.py", line 85, in on_connection_success
    main_window.on_connection_established(mode, params)
  File "E:\armband\src\gui\main_window.py", line 559, in on_connection_established
    self._send_armband_subscribed_command(True)
  File "E:\armband\src\gui\main_window.py", line 326, in _send_armband_subscribed_command
    self.serial_handler.write(self.armband_data_layer.get_armband_command())
TypeError: get_armband_command() missing 1 required positional argument: 'scribed'

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\PlotCurveItem.py", line 310, in boundingRect
    px, py = self.pixelVectors()
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\GraphicsItem.py", line 288, in pixelVectors
    pv = Point(dti.map(normView).p2()), Point(dti.map(normOrtho).p2())
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\Point.py", line 31, in __init__
    super().__init__(*args)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 609, in boundingRect
    def boundingRect(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 229, in on_import_tf_env_finished
    default_model_path = os.path.join(APP_ROOT_PATH, "models", model_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\ntpath.py", line 117, in join
    genericpath._check_arg_types('join', path, *paths)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\genericpath.py", line 152, in _check_arg_types
    raise TypeError(f'{funcname}() argument must be str, bytes, or '
TypeError: join() argument must be str, bytes, or os.PathLike object, not 'NoneType'

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 616, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 609, in boundingRect
    def boundingRect(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 621, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\connect_widget.py", line 332, in <lambda>
    card.clicked.connect(lambda : self.on_mode_selected(mode_id))
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 647, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\GraphicsWidget.py", line 51, in boundingRect
    def boundingRect(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\PlotCurveItem.py", line 316, in boundingRect
    py = 0 if py is None else py.length()
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 647, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---
