
--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 528, in on_intuitive_action_data_start_signal
    self.start_data_logging(action_name)
  File "E:\armband\src\gui\main_window.py", line 538, in start_data_logging
    with open(self.armband_data_layer.current_emg_file, 'w+', newline="") as f:
FileNotFoundError: [Errno 2] No such file or directory: 'sample\\放松_0_0.txt'

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 616, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 606, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 606, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 609, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\main.py", line 85, in on_connection_success
    main_window.on_connection_established(mode, params)
  File "E:\armband\src\gui\main_window.py", line 559, in on_connection_established
    self._send_armband_subscribed_command(True)
  File "E:\armband\src\gui\main_window.py", line 326, in _send_armband_subscribed_command
    self.serial_handler.write(self.armband_data_layer.get_armband_command())
TypeError: get_armband_command() missing 1 required positional argument: 'scribed'

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\PlotCurveItem.py", line 310, in boundingRect
    px, py = self.pixelVectors()
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\GraphicsItem.py", line 288, in pixelVectors
    pv = Point(dti.map(normView).p2()), Point(dti.map(normOrtho).p2())
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\Point.py", line 31, in __init__
    super().__init__(*args)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 609, in boundingRect
    def boundingRect(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 229, in on_import_tf_env_finished
    default_model_path = os.path.join(APP_ROOT_PATH, "models", model_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\ntpath.py", line 117, in join
    genericpath._check_arg_types('join', path, *paths)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\genericpath.py", line 152, in _check_arg_types
    raise TypeError(f'{funcname}() argument must be str, bytes, or '
TypeError: join() argument must be str, bytes, or os.PathLike object, not 'NoneType'

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 616, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 609, in boundingRect
    def boundingRect(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 621, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\connect_widget.py", line 332, in <lambda>
    card.clicked.connect(lambda : self.on_mode_selected(mode_id))
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\AxisItem.py", line 646, in paint
    self.picture.play(p)
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 647, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\GraphicsWidget.py", line 51, in boundingRect
    def boundingRect(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "D:\Envs_39\armband_control_tool\lib\site-packages\pyqtgraph\graphicsItems\PlotCurveItem.py", line 316, in boundingRect
    py = 0 if py is None else py.length()
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 647, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\utils\data_handler.py", line 58, in on_received_device_raw_message
    def on_received_device_raw_message(self, data: bytes):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\src\gui\main_window.py", line 647, in plot_armband_data
    def plot_armband_data(self):
KeyboardInterrupt

--- 错误结束 ---

--- main.py 错误 ---
Traceback (most recent call last):
  File "E:\armband\main.py", line 104, in <module>
    main()
  File "E:\armband\main.py", line 69, in main
    register_proto()
  File "E:\armband\main.py", line 55, in register_proto
    register_proto_class(proto_path)
  File "E:\armband\src\config\register_proto.py", line 76, in register_proto_class
    _reload_all(proto_path)
  File "E:\armband\src\config\register_proto.py", line 47, in _reload_all
    mod = importlib.import_module(modname)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\importlib\__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "E:\armband\.\edu-dongle-sensor-protobuf\generated\python\app_dongle\app_dongle_common_pb2.py", line 5, in <module>
    from google.protobuf.internal import builder as _builder
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 846, in exec_module
  File "<frozen importlib._bootstrap_external>", line 941, in get_code
  File "<frozen importlib._bootstrap_external>", line 1039, in get_data
KeyboardInterrupt

--- 错误结束 ---
