# edu-dongle-sensor-protobuf

该协议适用于高校项目中下面这些设备，需要通过Dongle 再和PC端APP通信的设备。

|设备|描述|
|--|--|
|EEG|单通道EEG传感器|
|EMG|单通道EMG传感器|
|ECG|单通道ECG传感器|
|PPG|单通道PPG传感器|
|ARMBAND|臂环：8通道EMG传感器 + 9轴|
|手套|手套：6通道FSR传感器 + 9轴|

## 需求
- Dongle充当USB虚拟串口和BLE主机数据透传功能，主要是实现稳定可靠的BLE数据传输。
- 设备和Dongle需要1对1绑定
- Dongle上电后，主动连接绑定好的设备

## 框图
整个系统应该分为3个角色：
- Device
- Dongle
- APP/产测软件

![框图](./resource/edu-diagram.png)



## 识别Dongle和设备

### 识别Dongle
- 通过识别USB信息来判断是否

|设备|厂家|产品|VID|PID|
|--|--|--|--|--|
|单通道EEG传感器|BrainCo|EEG Sensor|0x5243|0x0001|
|单通道EMG传感器|BrainCo|EMG Sensor|0x5243|0x0002|
|单通道ECG传感器|BrainCo|ECG Sensor|0x5243|0x0003|
|单通道PPG传感器|BrainCo|PPG Sensor|0x5243|0x0004|
|臂环|BrainCo|ARMBAND|0x5243|0x0005|
|手套|BrainCo|GLOVE|0x5243|0x0006|
|控制盒|BrainCo|Control Box|0x5243|0x0007|

- 通过协议中DeviceInfo来识别。和识别设备一样。



### 识别设备

- 通过协议中DeviceInfo来识别
```protobuf
message DeviceInfo {
	string modle = 1;
	string fw_version = 2;
	string hw_version = 3;
}
```

### Pair Key 生成
pair_key 用于 Dongle 和 Sensor 之间进行配对。pair_key 的生成逻辑如下:
```
  /* 对 Sensor mac 地址后四字节按位取反 */
  pair_key = ~(mac[2] << 24 | mac[3] << 16 | mac[4] << 8 | mac[5]);
```

## 协议

- Packet包：参考 [BrainCoTech/FW_ProjectID (github.com)](https://github.com/BrainCoTech/FW_ProjectID) 中定义。目前这些设备都公用一个Project ID。
  - APP: 1
  - Dongle: 2
  - Device: 3 或者 n，是否需要给不同Sensor 分配不同 ID

- Protobuf：见proto文件
