

import time
import threading
import queue
from typing import Optional, Callable, List, Union, Any, Dict
from dataclasses import dataclass
from enum import Enum
import serial
from serial.tools import list_ports
from loguru import logger


class SerialStatus(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


@dataclass
class SerialConfig:
    port: str = "COM1"
    baudrate: int = 115200
    bytesize: int = serial.EIGHTBITS
    parity: str = serial.PARITY_NONE
    stopbits: int = serial.STOPBITS_ONE
    timeout: float = 0.05
    write_timeout: float = 0.05
    xonxoff: bool = False
    rtscts: bool = False
    dsrdtr: bool = False


class SerialCommunication:

    def __init__(self, config: Optional[SerialConfig] = None):
        self.config = config or SerialConfig()
        self.serial_port: Optional[serial.Serial] = None
        self.status = SerialStatus.DISCONNECTED
        self._read_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._write_queue = queue.Queue()
        
        # 回调函数
        self.on_data_received: Optional[Callable[[bytes], None]] = None
        self.on_status_changed: Optional[Callable[[SerialStatus], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        
        # 数据缓存
        self._received_data = queue.Queue()
        self._lock = threading.Lock()
    
    def connect(self, config: Optional[SerialConfig] = None) -> bool:
        if config:
            self.config = config
            
        if self.is_connected():
            logger.warning("串口已连接")
            return True
            
        try:
            self._set_status(SerialStatus.CONNECTING)
            
            # 创建串口连接
            self.serial_port = serial.Serial(
                port=self.config.port,
                baudrate=self.config.baudrate,
                bytesize=self.config.bytesize,
                parity=self.config.parity,
                stopbits=self.config.stopbits,
                timeout=self.config.timeout,
                write_timeout=self.config.write_timeout,
                xonxoff=self.config.xonxoff,
                rtscts=self.config.rtscts,
                dsrdtr=self.config.dsrdtr
            )
            
            if self.serial_port.is_open:
                self._set_status(SerialStatus.CONNECTED)
                logger.info(f"串口连接成功: {self.config.port}")

                self.start_threads()

                return True
            else:
                raise Exception("串口打开失败")
                
        except Exception as e:
            self._set_status(SerialStatus.ERROR)
            error_msg = f"串口连接失败: {e}"
            logger.error(error_msg)
            if self.on_error:
                self.on_error(e)
            return False
    
    def disconnect(self):
        try:
            self._stop_event.set()
            
            # 等待线程结束
            if self._read_thread and self._read_thread.is_alive():
                self._read_thread.join(timeout=2.0)
                
            # 关闭串口
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                
            self.serial_port = None
            self._set_status(SerialStatus.DISCONNECTED)
            logger.info("串口已断开")
            
        except Exception as e:
            logger.error(f"断开串口时发生错误: {e}")
            if self.on_error:
                self.on_error(e)
    
    def is_connected(self) -> bool:
        return (self.serial_port is not None and
                self.serial_port.is_open and 
                self.status == SerialStatus.CONNECTED)
    
    def write(self, data: Union[str, bytes]) -> bool:
        if not self.is_connected():
            logger.error("串口未连接，无法发送数据")
            return False
            
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
                
            self._write_queue.put(data)
            return True
            
        except Exception as e:
            logger.error(f"发送数据失败: {e}")
            if self.on_error:
                self.on_error(e)
            return False
    
    def read(self, timeout: Optional[float] = None) -> Optional[bytes]:
        try:
            return self._received_data.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def flush_input(self):
        if self.is_connected():
            self.serial_port.reset_input_buffer()
        # 清空接收数据队列
        while not self._received_data.empty():
            try:
                self._received_data.get_nowait()
            except queue.Empty:
                break
    
    def flush_output(self):
        if self.is_connected():
            self.serial_port.reset_output_buffer()
        # 清空发送数据队列
        while not self._write_queue.empty():
            try:
                self._write_queue.get_nowait()
            except queue.Empty:
                break
    
    def _set_status(self, status: SerialStatus):
        with self._lock:
            if self.status != status:
                self.status = status
                logger.debug(f"串口状态变更: {status.value}")
                if self.on_status_changed:
                    self.on_status_changed(status)
    
    def start_threads(self):
        self._stop_event.clear()
        self._read_thread = threading.Thread(target=self._run_worker, daemon=True)
        self._read_thread.start()

    def stop_threads(self):
        self._stop_event.set()
        if self._read_thread:
            self._read_thread.join()
            self._read_thread = None

    def _run_worker(self):
        while not self._stop_event.is_set() and self.is_connected():
            try:
                # 判断是否有写入指令
                try:
                    write_command = self._write_queue.get_nowait()
                    self.serial_port.write(write_command)
                    time.sleep(0.05)
                except queue.Empty:
                    pass

                data = self.serial_port.read(1024)
                if data:
                    self._received_data.put(data)
                    if self.on_data_received:
                        self.on_data_received(data)
                else:
                    time.sleep(0.05)  # 避免CPU占用过高
                    
            except Exception as e:
                logger.error(f"读取数据时发生错误: {e}")
                if self.on_error:
                    self.on_error(e)
                break
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()
