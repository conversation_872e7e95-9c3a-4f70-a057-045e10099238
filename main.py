import os
import sys
import traceback

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

from src.config.register_proto import register_proto_class, list_proto_class
from src.path import APP_ROOT_PATH
from src.utils.config_loader import register_parameters_to_global, get_config
from src.utils.loguru_settings import setup_loguru
from src.gui.connect_widget import ConnectionInterface
from src.gui.main_window import MainWindow


def exception_hook(exc_type, exc_value, exc_traceback):
    """全局异常处理函数"""
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"发生错误:\n{error_msg}")

    # 将错误写入文件
    with open("error_log.txt", "a", encoding="utf-8") as f:
        f.write(f"\n--- {os.path.basename(__file__)} 错误 ---\n")
        f.write(error_msg)
        f.write("\n--- 错误结束 ---\n")

    # 如果Qt应用已经初始化，显示错误对话框
    if QApplication.instance():
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setText("程序发生错误")
        msg_box.setInformativeText("详细错误信息已写入error_log.txt文件")
        msg_box.setDetailedText(error_msg)
        msg_box.exec()

    # 调用原始的异常处理器
    sys.__excepthook__(exc_type, exc_value, exc_traceback)


def register_proto():
    _PROTO_PATH_STRUCTURE = get_config("PROTO_CONFIG.proto_path_structure")
    _PACK_TYPE = "-F"
    if getattr(sys, 'frozen', False):  # 打包
        if _PACK_TYPE == '-D':
            proto_path = os.path.realpath(os.path.join(APP_ROOT_PATH, *_PROTO_PATH_STRUCTURE[1]))
        else:
            exe_temp_path = sys._MEIPASS  # TEMP
            proto_path = os.path.realpath(os.path.join(exe_temp_path, *_PROTO_PATH_STRUCTURE[1]))
    elif __file__:  # 非打包
        proto_path = os.path.join(APP_ROOT_PATH, *_PROTO_PATH_STRUCTURE[0], *_PROTO_PATH_STRUCTURE[1])

    print("PROTO_PYTHON_PATH:", proto_path)
    if not os.path.exists(proto_path):
        raise FileNotFoundError(f"proto_path : {proto_path} not exists")
    register_proto_class(proto_path)
    print(list_proto_class())


def main():
    # 设置全局异常处理
    sys.excepthook = exception_hook

    setup_loguru(log_folder_path="log")

    # 注册全局参数
    register_parameters_to_global(os.path.join(APP_ROOT_PATH, "settings.yaml"))

    # 注册proto
    register_proto()

    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # todo 设置应用程序图标和信息

    # 定义显示连接对话框的函数
    def show_connection_dialog():
        connect_dialog = ConnectionInterface()

        def on_connection_success(mode, params):
            # 创建主窗口（但不显示）
            main_window = MainWindow()
            main_window.on_connection_established(mode, params)
            main_window.show()
            QTimer.singleShot(100, main_window.init_predictor_env)

        connect_dialog.connection_established.connect(on_connection_success)

        result = connect_dialog.exec()
        if result == 0:  # QDialog.Rejected
            print("Dialog rejected, exiting...")

            sys.exit(0)

    # 首次显示连接对话框
    show_connection_dialog()

    sys.exit(app.exec())


if __name__ == '__main__':
    main()
