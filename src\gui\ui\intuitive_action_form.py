# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'intuitive_action_form.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, QCursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QProgressBar, QPushButton,
    QSizePolicy, QWidget)

class Ui_ActionForm(object):
    def setupUi(self, ActionForm):
        if not ActionForm.objectName():
            ActionForm.setObjectName(u"ActionForm")
        ActionForm.resize(262, 26)
        ActionForm.setStyleSheet(u"*{\n"
"	font: 11pt;\n"
"}")
        self.gridLayout = QGridLayout(ActionForm)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.start_pushbutton = QPushButton(ActionForm)
        self.start_pushbutton.setObjectName(u"start_pushbutton")

        self.gridLayout.addWidget(self.start_pushbutton, 0, 0, 1, 1)

        self.progressBar = QProgressBar(ActionForm)
        self.progressBar.setObjectName(u"progressBar")
        self.progressBar.setValue(24)
        self.progressBar.setTextVisible(False)

        self.gridLayout.addWidget(self.progressBar, 0, 1, 1, 1)

        self.delete_pushbutton = QPushButton(ActionForm)
        self.delete_pushbutton.setObjectName(u"delete_pushbutton")
        sizePolicy = QSizePolicy(QSizePolicy.Minimum, QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.delete_pushbutton.sizePolicy().hasHeightForWidth())
        self.delete_pushbutton.setSizePolicy(sizePolicy)
        self.delete_pushbutton.setMaximumSize(QSize(30, 30))

        self.gridLayout.addWidget(self.delete_pushbutton, 0, 2, 1, 1)


        self.retranslateUi(ActionForm)

        QMetaObject.connectSlotsByName(ActionForm)
    # setupUi

    def retranslateUi(self, ActionForm):
        ActionForm.setWindowTitle(QCoreApplication.translate("ActionForm", u"Form", None))
        self.start_pushbutton.setText("")
        self.delete_pushbutton.setText(QCoreApplication.translate("ActionForm", u"\u2716", None))
    # retranslateUi

