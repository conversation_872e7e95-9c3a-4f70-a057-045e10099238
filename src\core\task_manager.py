import asyncio
import threading
import time
from enum import IntEnum
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

from src.config.config import ControlMode
from src.config.register_proto import get_proto_class
from src.core.armband_data_layer import Armband<PERSON><PERSON><PERSON>ayer
from src.utils.data_handler import ParseNode
from src.utils.tool_packet import ToolPacket


HAND_CONTROL_INTERVAL_S = 0.2


class TaskManager:
    def __init__(self,
                 armband_data_layer: ArmbandDataLayer,
                 modbus_handler=None,
                 robot_handler=None,
                 max_workers: int = 4):

        self.armband_data_layer = armband_data_layer
        self._max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._thread_lock = threading.RLock()

        self.control_enable = False
        self._current_mode = ControlMode.HAND
        self._last_error = None

        self._modbus_handler = modbus_handler
        self._robot_handler = robot_handler

        self._last_hand_command = None
        self._last_arm_command = None
        self._stop_event = threading.Event()  # 新增线程停止事件

        # 开启一个线程一直从armband_data_layer取值进行控制
        self._executor.submit(self._control_loop, armband_data_layer)

    def _control_loop(self, armband_data_layer):
        while not self._stop_event.is_set():
            if not self.control_enable:
                time.sleep(2)
                continue

            mode = self._current_mode

            print(55555555555555, mode)

            if mode == ControlMode.HAND:
                finger_id = self.armband_data_layer.finger_target_id
                finger_positions = self.armband_data_layer.get_finer_id_target_positions()
                self._handle_hand_mode(finger_id, finger_positions)
            elif mode == ControlMode.ARM_HAND:
                finger_id = self.armband_data_layer.finger_target_id
                finger_positions = self.armband_data_layer.get_finer_id_target_positions()
                self._handle_arm_hand_mode(finger_id, finger_positions)
            elif mode == ControlMode.ARM:
                arm_target_positions = self.armband_data_layer.arm_target_position
                if arm_target_positions is not None:
                    emg_data_values = self.armband_data_layer.afe_data_list
                    self._handle_arm_mode(arm_target_positions, emg_data_values)
            else:
                finger_id = self.armband_data_layer.finger_target_id
                finger_positions = self.armband_data_layer.get_finer_id_target_positions()
                arm_target_positions = self.armband_data_layer.arm_target_position
                emg_data_values = self.armband_data_layer.afe_data_list
                self._handle_cooperative_mode(finger_id, finger_positions, arm_target_positions, emg_data_values)

    def _handle_cooperative_mode(self, finger_id, finger_positions, arm_target_positions, emg_data_values):
        print(111111111111111111111, finger_id, finger_positions, 55555, arm_target_positions)
        if finger_id != 0:  # 放松就保持当前动作
            if self._last_hand_command is None or finger_positions != self._last_hand_command:
                self._last_hand_command = finger_positions
                self.send_hand_command_sync(1010, finger_positions)

        if arm_target_positions is not None:
            if self._last_hand_command is None or [arm_target_positions, emg_data_values] != self._last_arm_command:
                self._last_arm_command = [arm_target_positions, emg_data_values]
                self.send_arm_command_sync(arm_target_positions, emg_data_values)
                print(44444444444444444444444)
        time.sleep(HAND_CONTROL_INTERVAL_S)

    def _handle_hand_mode(self, finger_id, finger_positions):
        if finger_id == 0:  # 放松就保持当前动作
            time.sleep(HAND_CONTROL_INTERVAL_S)
            return
        if self._last_hand_command is None or finger_positions != self._last_hand_command:
            self._last_hand_command = finger_positions
            self.send_hand_command_sync(1010, finger_positions)
        time.sleep(HAND_CONTROL_INTERVAL_S)

    def _handle_arm_hand_mode(self, finger_id, finger_positions):
        if finger_id == 0:  # 放松就保持当前动作
            time.sleep(HAND_CONTROL_INTERVAL_S)
            return
        if self._last_hand_command is None or finger_positions != self._last_hand_command:
            self._last_hand_command = finger_positions
            self.send_finger_command_sync(finger_positions)
        time.sleep(HAND_CONTROL_INTERVAL_S)

    def _handle_arm_mode(self, arm_positions, emg_values):
        if self._last_hand_command is None or [arm_positions, emg_values] != self._last_arm_command:
            self._last_arm_command = [arm_positions, emg_values]
            self.send_arm_command_sync(arm_positions, emg_values)
        time.sleep(HAND_CONTROL_INTERVAL_S)

    def __del__(self):
        try:
            if self._executor:
                self._executor.shutdown(wait=False)
        except:
            pass

    def set_handlers(self, modbus_handler=None, robot_handler=None):
        if modbus_handler:
            self._modbus_handler = modbus_handler
            logger.info("Modbus handler set")

        if robot_handler:
            self._robot_handler = robot_handler
            logger.info("Robot handler set")

    def set_control_mode(self, mode: ControlMode):
        with self._thread_lock:
            old_mode = self._current_mode
            self._current_mode = mode

    def get_current_mode(self) -> ControlMode:
        return self._current_mode

    def get_last_error(self) -> Optional[str]:
        return self._last_error

    async def _execute_hand_control(self, command_data: Dict[str, Any]) -> bool:
        try:
            if not self._modbus_handler:
                raise Exception("Modbus handler not available")

            address = command_data.get('address')
            if address is None:
                raise Exception("Address not specified in command_data")

            if 'values' in command_data:
                values = command_data['values']

                if len(values) == 1:
                    success = await asyncio.get_event_loop().run_in_executor(
                        self._executor,
                        self._modbus_handler.write_holding_register,
                        address, values[0]
                    )
                elif len(values) > 1:
                    success = await asyncio.get_event_loop().run_in_executor(
                        self._executor,
                        self._modbus_handler.write_holding_registers,
                        address, values
                    )
                else:
                    raise Exception("Invalid number of values specified")

                if success:
                    logger.debug(f"Hand control: wrote {len(values)} values to registers starting at {address}")
                    return True
                else:
                    raise Exception(f"Failed to write registers starting at {address}")
            else:
                raise Exception("Neither 'value' nor 'values' specified in command_data")

        except Exception as e:
            logger.error(f"Hand control error: {e}")
            raise

    async def _execute_arm_control(self, command_data: Dict[str, Any]) -> bool:

        try:
            if not self._robot_handler:
                raise Exception("Robot handler not available")

            values = command_data.get('values')
            if values is None:
                raise Exception("Values not specified in command_data")

            emg_values = command_data.get('emg_values')

            # 在线程池中执行arm_control
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                self._robot_handler.arm_control,
                values, emg_values
            )

            logger.debug(f"Arm control executed with {len(values)} values")
            return True

        except Exception as e:
            logger.error(f"Arm control error: {e}")
            raise

    async def _execute_arm_hand_control(self, command_data: Dict[str, Any]) -> bool:
        """
        执行臂带手模式 - 调用REMRobotModbusHandler的finger_control

        Args:
            command_data: 包含finger_data和control_mode的字典
            例如: {'finger_data': [1,2,3,4,5,6], 'control_mode': 'pos'}
        """
        try:
            if not self._robot_handler:
                raise Exception("Robot handler not available")

            finger_data = command_data.get('finger_data')
            if finger_data is None:
                raise Exception("finger_data not specified in command_data")

            control_mode = command_data.get('control_mode', 'pos')  # 默认位置控制

            # 在线程池中执行finger_control
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                self._robot_handler.finger_control,
                finger_data, control_mode
            )

            logger.debug(f"Arm-hand control executed with {len(finger_data)} finger values, mode: {control_mode}")
            return True

        except Exception as e:
            logger.error(f"Arm-hand control error: {e}")
            raise

    async def _execute_cooperative_control(self, command_data: Dict[str, Any]) -> bool:
        """
        执行协同控制模式 - 同时调用arm_control和finger_control

        Args:
            command_data: 包含arm和finger控制数据的字典
            例如: {
                'arm_data': {'values': [1,2,3,4,5,6], 'emg_values': [[1,2],[3,4]]},
                'finger_data': {'finger_data': [1,2,3,4,5,6], 'control_mode': 'pos'}
            }
        """
        try:
            if not self._robot_handler:
                raise Exception("Robot handler not available")

            arm_data = command_data.get('arm_data')
            finger_data = command_data.get('finger_data')

            if not arm_data and not finger_data:
                raise Exception("Neither arm_data nor finger_data specified")

            # 创建并发任务列表
            tasks = []

            # 添加arm控制任务
            if arm_data:
                values = arm_data.get('values')
                if values:
                    emg_values = arm_data.get('emg_values')
                    task = asyncio.get_event_loop().run_in_executor(
                        self._executor,
                        self._robot_handler.arm_control,
                        values, emg_values
                    )
                    tasks.append(('arm', task))

            # 添加finger控制任务
            if finger_data:
                finger_values = finger_data.get('finger_data')
                if finger_values:
                    control_mode = finger_data.get('control_mode', 'pos')
                    task = asyncio.get_event_loop().run_in_executor(
                        self._executor,
                        self._robot_handler.finger_control,
                        finger_values, control_mode
                    )
                    tasks.append(('finger', task))

            if not tasks:
                raise Exception("No valid control data provided")

            # 并发执行所有任务
            results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

            # 检查结果
            success_count = 0
            for i, (task_type, result) in enumerate(zip([name for name, _ in tasks], results)):
                if isinstance(result, Exception):
                    logger.error(f"Cooperative control {task_type} task failed: {result}")
                else:
                    success_count += 1
                    logger.debug(f"Cooperative control {task_type} task completed successfully")

            if success_count == 0:
                raise Exception("All cooperative control tasks failed")
            elif success_count < len(tasks):
                logger.warning(f"Only {success_count}/{len(tasks)} cooperative control tasks succeeded")

            logger.debug(f"Cooperative control executed: {success_count}/{len(tasks)} tasks succeeded")
            return True

        except Exception as e:
            logger.error(f"Cooperative control error: {e}")
            raise

    async def send_hand_command(self, address: int, values: List[int]) -> bool:
        try:
            return await self._execute_hand_control({'address': address, 'values': values})
        except Exception as e:
            self._last_error = f"Failed to send hand command: {str(e)}"
            logger.error(self._last_error)
            return False

    async def send_arm_command(self, values: List, emg_values: List = None) -> bool:
        try:
            command_data = {'values': values}
            if emg_values is not None:
                command_data['emg_values'] = emg_values
            return await self._execute_arm_control(command_data)
        except Exception as e:
            self._last_error = f"Failed to send arm command: {str(e)}"
            logger.error(self._last_error)
            return False

    async def send_finger_command(self, finger_data: List, control_mode: str = 'pos') -> bool:
        try:
            command_data = {
                'finger_data': finger_data,
                'control_mode': control_mode
            }
            return await self._execute_arm_hand_control(command_data)
        except Exception as e:
            self._last_error = f"Failed to send finger command: {str(e)}"
            logger.error(self._last_error)
            return False

    async def send_cooperative_command(self,
                                     arm_values: List = None,
                                     emg_values: List = None,
                                     finger_data: List = None,
                                     finger_control_mode: str = 'pos') -> bool:
        try:
            command_data = {}

            if arm_values is not None:
                arm_data = {'values': arm_values}
                if emg_values is not None:
                    arm_data['emg_values'] = emg_values
                command_data['arm_data'] = arm_data

            if finger_data is not None:
                command_data['finger_data'] = {
                    'finger_data': finger_data,
                    'control_mode': finger_control_mode
                }

            return await self._execute_cooperative_control(command_data)
        except Exception as e:
            self._last_error = f"Failed to send cooperative command: {str(e)}"
            logger.error(self._last_error)
            return False

    def send_hand_command_sync(self, address: int, values: List[int]) -> bool:
        try:
            try:
                loop = asyncio.get_running_loop()
                future = asyncio.run_coroutine_threadsafe(
                    self.send_hand_command(address, values), loop
                )
                return future.result(timeout=10.0)  # 10秒超时
            except RuntimeError:
                return asyncio.run(self.send_hand_command(address, values))
        except Exception as e:
            self._last_error = f"Failed to send hand command sync: {str(e)}"
            logger.error(self._last_error)
            return False

    def send_arm_command_sync(self, values: List, emg_values: List = None) -> bool:
        try:
            try:
                loop = asyncio.get_running_loop()
                future = asyncio.run_coroutine_threadsafe(
                    self.send_arm_command(values, emg_values), loop
                )
                return future.result(timeout=10.0)
            except RuntimeError:
                return asyncio.run(self.send_arm_command(values, emg_values))
        except Exception as e:
            self._last_error = f"Failed to send arm command sync: {str(e)}"
            logger.error(self._last_error)
            return False

    def send_finger_command_sync(self, finger_data: List, control_mode: str = 'pos') -> bool:
        try:
            try:
                loop = asyncio.get_running_loop()
                future = asyncio.run_coroutine_threadsafe(
                    self.send_finger_command(finger_data, control_mode), loop
                )
                return future.result(timeout=10.0)
            except RuntimeError:
                return asyncio.run(self.send_finger_command(finger_data, control_mode))
        except Exception as e:
            self._last_error = f"Failed to send finger command sync: {str(e)}"
            logger.error(self._last_error)
            return False

    def send_cooperative_command_sync(self,
                                    arm_values: List = None,
                                    emg_values: List = None,
                                    finger_data: List = None,
                                    finger_control_mode: str = 'pos') -> bool:
        """
        同步版本：发送协同控制命令

        Args:
            arm_values: 臂控制值列表
            emg_values: EMG值列表
            finger_data: 手指数据列表
            finger_control_mode: 手指控制模式

        Returns:
            bool: 执行成功返回True
        """
        try:
            try:
                loop = asyncio.get_running_loop()
                future = asyncio.run_coroutine_threadsafe(
                    self.send_cooperative_command(arm_values, emg_values, finger_data, finger_control_mode),
                    loop
                )
                return future.result(timeout=15.0)  # 协同控制可能需要更长时间
            except RuntimeError:
                return asyncio.run(
                    self.send_cooperative_command(arm_values, emg_values, finger_data, finger_control_mode)
                )
        except Exception as e:
            self._last_error = f"Failed to send cooperative command sync: {str(e)}"
            logger.error(self._last_error)
            return False

    def control_hand(self, register_address: int, register_values: list) -> bool:
        return self.send_hand_command_sync(address=register_address, values=register_values)

    def control_arm(self, control_values: List[float], emg_data: List[List[float]] = None) -> bool:
        return self.send_arm_command_sync(values=control_values, emg_values=emg_data)

    def control_fingers(self, finger_positions: List[int], mode: str = 'position') -> bool:
        control_mode = 'pos' if mode.lower() in ['position', 'pos'] else 'speed'
        return self.send_finger_command_sync(finger_data=finger_positions, control_mode=control_mode)

    def control_arm_and_hand(self,
                           arm_values: List[float] = None,
                           finger_values: List[int] = None,
                           emg_data: List[List[float]] = None,
                           finger_mode: str = 'position') -> bool:
        """
        外部上位机接口：协同控制臂和手

        Args:
            arm_values: 臂控制值列表（可选）
            finger_values: 手指控制值列表（可选）
            emg_data: EMG数据（可选）
            finger_mode: 手指控制模式

        Returns:
            bool: 执行成功返回True
        """
        # 转换模式名称
        control_mode = 'pos' if finger_mode.lower() in ['position', 'pos'] else 'speed'
        return self.send_cooperative_command_sync(
            arm_values=arm_values,
            emg_values=emg_data,
            finger_data=finger_values,
            finger_control_mode=control_mode
        )

    def stop_control_loop(self):
        """显式停止控制线程"""
        self._stop_event.set()
        if self._executor:
            self._executor.shutdown(wait=False)

