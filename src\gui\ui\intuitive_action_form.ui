<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ActionForm</class>
 <widget class="QWidget" name="ActionForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>262</width>
    <height>26</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 11pt;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QPushButton" name="start_pushbutton">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QProgressBar" name="progressBar">
     <property name="value">
      <number>24</number>
     </property>
     <property name="textVisible">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QPushButton" name="delete_pushbutton">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>30</height>
      </size>
     </property>
     <property name="text">
      <string>✖</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
